{"scenario_main_init": "scenario_abm_init.json", "scenario_main": "scenario_abm.json", "num_abm_runs": "3", "output_directories": "bloomington_abm", "num_threads": "10", "database_base_name": "bloomington", "model": "/lcrc/project/POLARIS/bebop/polaris/model/Integrated_Model", "scripts_dir": "/lcrc/project/POLARIS/bebop/polaris/data/scripts", "standard_dir": "bloomington_abm", "results_dir": "convergence_results"}
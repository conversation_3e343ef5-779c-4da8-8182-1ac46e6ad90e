{"python_path": "C:/Program Files/Python36/", "sqlite3_path": "%CD%\\third_party\\Sqlite3\\", "tail_app": "/mnt/d/rweimer/polaris/polaris-hpc/convergence/tail.sh", "cloud_backup_path": "\\\\vms-fs2\\VMS_FY19_SMART_Runs\\Chicago_ABM_Convergence2018\\Chicago_202000406_results\\", "scenario_skim_init": "scenario_skim_init.json", "scenario_main_init": "scenario_abm.json", "scenario_main": "scenario_abm.json", "num_abm_runs": "3", "output_directories": "bloomington_abm", "num_threads": "10", "database_base_name": "bloomington", "model": "/mnt/d/rweimer/polaris/polaris-20201204/build/release/bin/Integrated_Model", "data_no_longer_used": "/mnt/d/rweimer/polaris/models/Bloomington_For_Randy", "scripts_dir": "/mnt/d/rweimer/polaris/models/scripts", "standard_dir": "bloomington_abm", "results_dir": "convergence_results"}
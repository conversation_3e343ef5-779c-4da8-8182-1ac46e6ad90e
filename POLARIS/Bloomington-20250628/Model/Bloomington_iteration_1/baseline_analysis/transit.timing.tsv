,duration,sql
0,0.00014209747314453125,DROP TABLE IF EXISTS boardings_by_agency_mode
1,0.036096811294555664,"CREATE TABLE boardings_by_agency_mode as
SELECT
    ta.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    4.0*sum(tvl.value_boardings) as boardings,
    4.0*sum(tvl.value_alightings) as alightings
FROM
    ""Transit_Vehicle_links"" tvl,
    transit_vehicle tv,
    a.transit_trips tt,
    a.transit_patterns tp,
    a.transit_routes tr,
    a.transit_agencies ta
where
    tvl.value_transit_vehicle_trip = tv.transit_vehicle_trip and
    tvl.value_transit_vehicle_trip = tt.trip_id and
    tp.pattern_id = tt.pattern_id and
    tr.route_id = tp.route_id AND
    tr.agency_id = ta.agency_id
group by 1,2
order by 1,2"
2,6.365776062011719e-05,DROP TABLE IF EXISTS boardings_by_agency_mode_time
3,0.022324085235595703,"CREATE TABLE boardings_by_agency_mode_time as
SELECT
    ta.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    cast(cast(cast(tvl.value_act_departure_time as real)/1800 as int) as real)/2 as HH,
    4.0*sum(tvl.value_boardings) as boardings,
    4.0*sum(tvl.value_alightings) as alightings
FROM
    ""Transit_Vehicle_links"" tvl,
    transit_vehicle tv,
    a.transit_trips tt,
    a.transit_patterns tp,
    a.transit_routes tr,
    a.transit_agencies ta
where
    tvl.value_transit_vehicle_trip = tv.transit_vehicle_trip and
    tvl.value_transit_vehicle_trip = tt.trip_id and
    tp.pattern_id = tt.pattern_id and
    tr.route_id = tp.route_id AND
    tr.agency_id = ta.agency_id
group by agency, mode, HH
order by agency, mode desc, HH"
4,4.673004150390625e-05,DROP TABLE IF EXISTS boardings_by_agency_mode_route_time
5,0.02376723289489746,"CREATE TABLE boardings_by_agency_mode_route_time as
SELECT
    ta.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    tr.route as route,
    cast(cast(cast(tvl.value_act_departure_time as real)/1800 as int) as real)/2 as HH,
    4.0*sum(tvl.value_boardings) as boardings,
    4.0*sum(tvl.value_alightings) as alightings
FROM
    ""Transit_Vehicle_links"" tvl,
    transit_vehicle tv,
    a.transit_trips tt,
    a.transit_patterns tp,
    a.transit_routes tr,
    a.transit_agencies ta
where
    tvl.value_transit_vehicle_trip = tv.transit_vehicle_trip and
    tvl.value_transit_vehicle_trip = tt.trip_id and
    tp.pattern_id = tt.pattern_id and
    tr.route_id = tp.route_id AND
    tr.agency_id = ta.agency_id
group by agency, mode, route, HH
order by agency, mode desc, route, HH"
6,0.00011420249938964844,DROP TABLE IF EXISTS boardings_by_agency_mode_route_stop_time
7,0.016503572463989258,"CREATE TABLE boardings_by_agency_mode_route_stop_time as
SELECT
    ta.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    tr.route as route,
    ts.stop as stop,
    cast(cast(cast(tvl.value_act_departure_time as real)/1800 as int) as real)/2 as HH,
    4.0*sum(tvl.value_boardings) as boardings
FROM
    ""Transit_Vehicle_links"" tvl,
    transit_vehicle tv,
    a.transit_trips tt,
    a.transit_patterns tp,
    a.transit_routes tr,
    a.transit_agencies ta,
    a.transit_links tl,
    a.transit_stops ts
where
    tvl.value_transit_vehicle_trip = tv.transit_vehicle_trip and
    tvl.value_transit_vehicle_trip = tt.trip_id and
    tp.pattern_id = tt.pattern_id and
    tr.route_id = tp.route_id AND
    tr.agency_id = ta.agency_id and
    tl.transit_link = tvl.value_link AND
    tl.from_node = ts.stop_id AND
    tl.type < 3
group by agency, mode, route, stop, HH
order by agency, mode desc, route, stop, HH"
8,4.3392181396484375e-05,DROP TABLE IF EXISTS boardings_by_agency_mode_area_type
9,0.021937131881713867,"CREATE TABLE boardings_by_agency_mode_area_type as
SELECT
    ta.agency as agency,
    tv.mode as mode,
    z.area_type as area_type,
    4.0*sum(tvl.value_boardings) as boardings,
    4.0*sum(tvl.value_alightings) as alightings
FROM
    ""Transit_Vehicle_links"" tvl,
    transit_vehicle tv,
    a.transit_trips tt,
    a.transit_patterns tp,
    a.transit_routes tr,
    a.transit_agencies ta,
    a.transit_stops ts,
    a.transit_links tl,
    a.zone z
where
    tvl.value_transit_vehicle_trip = tv.transit_vehicle_trip and
    tvl.value_transit_vehicle_trip = tt.trip_id and
    tp.pattern_id = tt.pattern_id and
    tr.route_id = tp.route_id and
    ta.agency_id = tr.agency_id AND
    tl.transit_link = tvl.value_link and
    ts.stop_id = tl.from_node and
    ts.zone = z.zone
group by
    ta.agency,
    tv.mode,
    z.area_type
order by
    ta.agency,
    tv.mode desc,
    z.area_type"
10,4.3392181396484375e-05,DROP TABLE IF EXISTS transit_vmt_pmt_occ
11,0.016526460647583008,"CREATE TABLE transit_vmt_pmt_occ as
SELECT
    a.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    sum(value_length)/1609.34 as VMT,
    4.0*sum((value_seated_load + value_standing_load)*value_length)/1609.34 as PMT,
    4.0*sum((value_seated_load + value_standing_load)*value_length)/sum(value_length) as Occupancy
FROM
    ""Transit_Vehicle_links"" l,
    a.transit_trips t,
    a.transit_patterns p,
    a.transit_routes tr,
    a.transit_agencies a
where
    l.value_transit_vehicle_trip = t.trip_id
    and t.pattern_id = p.pattern_id
    and p.route_id = tr.route_id
    and tr.agency_id = a.agency_id
group by 
    agency, mode"
12,4.38690185546875e-05,DROP TABLE IF EXISTS transit_vmt_pmt_occ_by_period
13,0.019365310668945312,"CREATE TABLE transit_vmt_pmt_occ_by_period as
SELECT
    ((l.value_act_arrival_time >= 6*3600.0 and l.value_act_arrival_time < 9*3600.0)
    or (l.value_act_arrival_time >= 15*3600.0 and l.value_act_arrival_time < 18*3600.0)) as peak_period,
    a.agency as agency,
    CASE WHEN tr.""type"" = 0  THEN 'TRAM'
                WHEN tr.""type"" = 1  THEN 'METRO'
                WHEN tr.""type"" = 2  THEN 'COMM'
                WHEN tr.""type"" = 3  THEN 'BUS'
                WHEN tr.""type"" = 4  THEN 'FERRY'
                WHEN tr.""type"" = 5  THEN 'CABLE'
                WHEN tr.""type"" = 6  THEN 'LIFT'
                WHEN tr.""type"" = 7  THEN 'FUNICULAR'
                WHEN tr.""type"" = 11 THEN 'TROLLEY'
                WHEN tr.""type"" = 12 THEN 'MONO'
	       END as ""mode"",
    sum(value_length)/1609.34 as VMT,
    4.0*sum((value_seated_load + value_standing_load)*value_length)/1609.34 as PMT,
    4.0*sum((value_seated_load + value_standing_load)*value_length)/sum(value_length) as Occupancy
FROM ""Transit_Vehicle_links"" l,
     a.transit_trips t,
     a.transit_patterns p,
     a.transit_routes tr,
     a.transit_agencies a
where l.value_transit_vehicle_trip = t.trip_id
  and t.pattern_id = p.pattern_id
  and p.route_id = tr.route_id
  and tr.agency_id = a.agency_id
group by peak_period, agency, mode"

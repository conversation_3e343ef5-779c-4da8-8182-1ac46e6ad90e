,duration,sql
0,0.00012874603271484375,DROP TABLE IF EXISTS Mode_Distribution_ADULT
1,0.2076120376586914,"CREATE TABLE IF NOT EXISTS mode_Distribution_ADULT AS
SELECT
    mode,
    4.0 * sum(trip.destination = person.work_location_id) AS 'HBW',
    4.0 * sum(trip.origin == household.location AND trip.destination <> person.work_location_id) AS 'HBO',
    4.0 * sum(trip.origin <> household.location AND trip.destination <> household.location AND trip.destination <> person.work_location_id) AS 'NHB',
    4.0 * count(*) AS total
FROM
    trip,
    person,
    household
WHERE
    trip.person = person.person
    AND person.household = household.household
    AND person.age > 16
    AND trip.""end"" - trip.""start"" > 2
GROUP BY
    mode"

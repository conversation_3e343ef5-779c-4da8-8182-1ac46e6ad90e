,duration,sql
0,6.914138793945312e-05,DROP TABLE IF EXISTS employment_validation
1,0.021338701248168945,"CREATE TABLE employment_validation as 
select l.zone, count(*)*4.0, z.employment_total
from person p, a.location l, a.zone z
where p.work_location_id = l.location  and l.zone = z.zone
group by l.zone"
2,5.435943603515625e-05,DROP TABLE IF EXISTS vmt_vht_by_mode_city
3,0.056801795959472656,"CREATE TABLE vmt_vht_by_mode_city as
SELECT mode, 
       4.0*sum(travel_distance)/1609.3/1000000.0 as million_VMT, 
       4.0*sum(end-start)/3600.0/1000000.0 as million_VHT, 
       4.0*sum(travel_distance)/1609.3/(4.0*sum(end-start)/3600.0) as speed_mph, 
       4.0*count(*) as count
FROM trip t, a.location as al, a.zone as az
where t.""end"" > t.start and t.origin = al.location and al.zone = az.zone and az.area_type <= 3 and has_artificial_trip <> 1
group BY mode"
4,6.580352783203125e-05,DROP TABLE IF EXISTS trips_in_network_city
5,0.12057042121887207,"CREATE TABLE trips_in_network_city as
select time, cum_depart, cum_arrive, cum_depart-cum_arrive as in_network 
from (select time, 
             4.0*sum(departures) OVER (ROWS UNBOUNDED PRECEDING) as cum_depart, 
         4.0*sum(arrivals) OVER (ROWS UNBOUNDED PRECEDING) as cum_arrive 
      from ( select time, sum(departures) as departures, sum(arrivals) as arrivals 
             from (select cast(""start""/6 as int)*6 as time, count(*) as departures, 0 as arrivals 
               from trip t, a.location l1, a.location l2
                   where mode = 0 and person is not null and t.origin = l1.location and t.destination = l2.location 
             and (l1.area_type <= 3 or l2.area_type <= 3)
             group by time
                   UNION
             select cast(""end""/6 as int)*6 as time, 0 as departures, count(*) as arrivals
             from trip t, a.location l1, a.location l2
             where mode = 0 and person is not null and t.origin = l1.location and t.destination = l2.location 
             and (l1.area_type <= 3 or l2.area_type <= 3)
             group by time
             UNION
             select cast(""start""/6 as int)*6 as time, count(*) as departures, 0 as arrivals
             from tnc_trip t, a.location l1, a.location l2
             where t.origin = l1.location and t.destination = l2.location and (l1.area_type <= 3 or l2.area_type <= 3)
             group by time
             UNION
             select cast(""end""/6 as int)*6 as time, 0 as departures, count(*) as arrivals
             from tnc_trip t, a.location l1, a.location l2
             where t.origin = l1.location and t.destination = l2.location and (l1.area_type <= 3 or l2.area_type <= 3)
             group by time
              )
             group by time
           )
     )"
6,0.0001533031463623047,DROP TABLE IF EXISTS planned_activity_mode_share
7,0.09701657295227051,"CREATE TABLE planned_activity_mode_share as
Select
    activity.mode, 4.0*count(*) as mode_count
FROM
    activity, person
WHERE
    activity.start_time > 122 and 
    activity.trip = 0 and
    activity.person = person.person and
    person.age > 16
GROUP BY
    activity.mode"
8,0.00013399124145507812,DROP TABLE IF EXISTS executed_activity_mode_share
9,0.17314386367797852,"CREATE TABLE executed_activity_mode_share as
Select
    activity.mode as mode, 4.0*count(*) as mode_count
FROM
    activity, person, trip
WHERE
    activity.start_time > 122 and 
    activity.trip = trip.trip_id and
    trip.""end"" - trip.""start"" > 2 and
    activity.person = person.person and
    person.age > 16 and
    activity.mode not like 'FAIL%'
GROUP BY
    activity.mode"
10,7.081031799316406e-05,DROP TABLE IF EXISTS executed_activity_mode_share_fails
11,0.09965395927429199,"CREATE TABLE executed_activity_mode_share_fails as
Select
    activity.mode as mode, 4.0*count(*) as mode_count
FROM
    activity, person
WHERE
    activity.start_time > 122 and 
    activity.trip <> 0 and
    activity.person = person.person and
    person.age > 16
GROUP BY
    activity.mode"
12,7.939338684082031e-05,DROP TABLE IF EXISTS planned_activity_mode_share_by_area
13,0.16074705123901367,"CREATE TABLE planned_activity_mode_share_by_area as
Select
    activity.type, a.zone.area_type, activity.mode, 4.0*count(*) as mode_count
FROM
    activity, person, a.location, a.zone
WHERE
    activity.start_time > 122 and
    activity.trip = 0 and
    activity.person = person.person and
    person.age > 16 and
    activity.location_id = a.location.location and a.location.zone = a.zone.zone
GROUP BY
    activity.type, a.zone.area_type, activity.mode"
14,6.532669067382812e-05,DROP TABLE IF EXISTS executed_activity_mode_share_by_area
15,0.20069456100463867,"CREATE TABLE executed_activity_mode_share_by_area as
Select
    activity.type, a.zone.area_type, activity.mode, 4.0*count(*) as mode_count
FROM
    activity, person, a.location, a.zone
WHERE
    activity.start_time > 122 AND
    activity.trip > 0 AND
    activity.person = person.person AND
    person.age > 16 AND
    activity.location_id = a.location.location and a.location.zone = a.zone.zone
GROUP BY
    activity.type, a.zone.area_type, activity.mode"
16,6.29425048828125e-05,DROP TABLE IF EXISTS executed_activity_dist_by_area
17,0.006608009338378906,"CREATE TABLE executed_activity_dist_by_area as
select type, sum(mode_count) as mode_count 
from ""executed_activity_mode_share_by_area""
group by type"
18,0.00011801719665527344,DROP TABLE IF EXISTS executed_activity_dist_by_area_city
19,0.007575511932373047,"CREATE TABLE executed_activity_dist_by_area_city as
select type, sum(mode_count) as mode_count 
from ""executed_activity_mode_share_by_area""
where area_type < 4
group by type"
20,0.00011324882507324219,DROP TABLE IF EXISTS there_is_path
21,0.07457566261291504,"CREATE TABLE there_is_path AS
SELECT path IS NOT NULL AS there_is_path,
       sum(abs(end-start-routed_travel_time))/sum(end-start) as relative_gap_abs,
       sum(max(end-start-routed_travel_time,0))/sum(end-start) as relative_gap_min0,
       count(*) as ""number_of_trips"" 
FROM trip
WHERE (mode = 0 or mode = 9 or mode = 17 or mode = 18 or mode = 19 or mode = 20)  and has_artificial_trip = 0
GROUP BY there_is_path"
22,0.00012993812561035156,DROP TABLE IF EXISTS gap_bins
23,0.0750894546508789,"CREATE TABLE gap_bins AS
SELECT cast(experienced_gap/0.1 as int)*0.1 as gap_bin, path is not null as there_is_path, count(*) 
FROM trip
WHERE (mode = 0 or mode = 9 or mode = 17 or mode = 18 or mode = 19 or mode = 20) and has_artificial_trip = 0
GROUP BY gap_bin, there_is_path
ORDER BY gap_bin, there_is_path DESC"
24,5.5789947509765625e-05,DROP TABLE IF EXISTS greater_routed_time
25,0.04107952117919922,"CREATE TABLE greater_routed_time as
select routed_travel_time > (end-start) as greater_routed_time, count(*)
from trip
where (mode = 0 or mode = 9 or mode = 17 or mode = 18 or mode = 19 or mode = 20) and has_artificial_trip = 0
group by greater_routed_time"
26,6.794929504394531e-05,DROP TABLE IF EXISTS mode_count
27,0.09183406829833984,"CREATE TABLE mode_count as
select CASE ""mode""
           WHEN 0  THEN 'SOV'
           WHEN 2  THEN 'HOV'
           WHEN 4  THEN 'BUS'
           WHEN 5  THEN 'RAIL'
           WHEN 6  THEN 'NONMOTORIZED'
           WHEN 7  THEN 'BICYCLE'
           WHEN 8  THEN 'WALK'
           WHEN 9  THEN 'TAXI'
           WHEN 10 THEN 'SCHOOLBUS'
           WHEN 11 THEN 'PARK_AND_RIDE'
           WHEN 12 THEN 'KISS_AND_RIDE'
           WHEN 13 THEN 'PARK_AND_RAIL'
           WHEN 14 THEN 'KISS_AND_RAIL'
           WHEN 15 THEN 'TNC_AND_RIDE'
           WHEN 16 THEN 'TNC_AND_RAIL'
           WHEN 25 THEN 'RIDE_AND_UNPARK'
           WHEN 26 THEN 'RIDE_AND_REKISS'
           WHEN 27 THEN 'RAIL_AND_UNPARK'
           WHEN 28 THEN 'RAIL_AND_REKISS'
           END as MODE_NAME, has_artificial_trip, 4.0*count(*) as mode_count from trip
group by MODE_NAME, has_artificial_trip
order by MODE_NAME, has_artificial_trip"
28,6.985664367675781e-05,DROP table IF EXISTS toll_revenue
29,0.02409076690673828,"CREATE TABLE toll_revenue as
SELECT person_toll, person_toll_count, tnc_toll, tnc_toll_count, 
person_toll + tnc_toll as total_toll, person_toll_count + tnc_toll_count as total_count
FROM
    (
        SELECT 4.0 * sum(toll) as person_toll, 4.0 * count(*) as person_toll_count
        FROM Trip
        WHERE type = 22 or ((mode == 0 or mode == 9) and type == 11)
    ) as t1,
    (
        SELECT 4.0 * sum(toll) as tnc_toll, 4.0 * count(*) as tnc_toll_count
        FROM TNC_Trip
    ) as t2"

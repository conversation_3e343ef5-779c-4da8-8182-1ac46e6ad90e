
DROP TABLE IF EXISTS activity_summary;
CREATE TABLE activity_summary AS
SELECT a."type"                 AS activity_type,
       CASE
           WHEN household.income <= 27026                               then 'QUINTILE_1'
           WHEN household.income <= 52179  and household.income > 27026 then 'QUINTILE_2'
           WHEN household.income <= 85076  and household.income > 52179 then 'QUINTILE_3'
           WHEN household.income <= 141110 and household.income > 85076 then 'QUINTILE_4'
           WHEN household.income > 141110                               then 'QUINTILE_5'
    end       AS income_quintile,
       CASE WHEN p.race = 1 THEN 'White_alone'
                WHEN p.race = 2 THEN 'Black_alone'
                WHEN p.race = 3 THEN 'American_Indian_alone'
                WHEN p.race = 4 THEN 'Alaskan_Native_alone'
                WHEN p.race = 5 THEN 'American_Indian_other'
                WHEN p.race = 6 THEN 'Asian_alone'
                WHEN p.race = 7 THEN 'Pacific_Islander_alone'
                WHEN p.race = 8 THEN 'Other_race_alone'
                WHEN p.race = 9 THEN 'Two_or_more_races'
           END                  AS race,
       
Case WHEN p.gender = 1 THEN 'Male'
     WHEN p.gender = 2 THEN 'Female'
END                AS gender,
       l1."zone"                AS household_zone,
       l2."zone"                AS activity_zone,
       4.0 * count() AS activity_count
FROM activity a, person p, household, a.location l1, a.location l2
WHERE a.start_time > 122
  AND a.trip > 0
  AND a.person = p.person
  AND p.household = household.household
  AND l1.location = household.location
  AND l2.location = a.location_id
GROUP BY activity_type, income_quintile, race, gender, household_zone, activity_zone;
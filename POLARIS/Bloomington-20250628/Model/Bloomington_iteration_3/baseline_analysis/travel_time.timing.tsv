,duration,sql
0,7.390975952148438e-05,DROP TABLE IF EXISTS ttime_By_ACT_Average
1,0.2605702877044678,"CREATE TABLE IF NOT EXISTS ttime_By_ACT_Average As
SELECT activity.type as acttype, 
       avg(trip.skim_travel_time)/60 as ttime_avg_skim, 
       avg(trip.routed_travel_time)/60 as ttime_avg_routed, 
       avg(trip.end - trip.start)/60 as ttime_avg, 
       avg(trip.travel_distance)/1000 as dist_avg, 
       4.0*count(*) as count
FROM trip, person, household, activity
WHERE trip.person = person.person 
AND person.household = household.household 
AND person.age > 16 
AND activity.trip = trip.trip_id 
AND travel_distance < 1000000 
AND trip.end - trip.start > 2
GROUP BY ACTTYPE"
2,6.031990051269531e-05,DROP TABLE IF EXISTS ttime_By_ACT_Average_w_skims
3,0.259326696395874,"CREATE TABLE IF NOT EXISTS ttime_By_ACT_Average_w_skims As
select activity.type as acttype, 
       avg(trip.skim_travel_time)/60 as ttime_avg_skim, 
       avg(trip.routed_travel_time)/60 as ttime_avg_routed, 
       avg(trip.end - trip.start)/60 as ttime_avg, 
       avg(trip.travel_distance)/1000 as dist_avg, 
       4.0*count(*) as count
from trip, person, household, activity
where trip.person = person.person 
and person.household = household.household 
and person.age > 16 
and activity.trip = trip.trip_id 
and travel_distance < 1000000 
and trip.end - trip.start >= 0
and trip.skim_travel_time >= 0
and trip.skim_travel_time < 86400
group by ACTTYPE"
4,0.00010204315185546875,DROP TABLE IF EXISTS ttime_By_ACT_Average_w_skims_hway
5,0.18346571922302246,"CREATE TABLE IF NOT EXISTS ttime_By_ACT_Average_w_skims_hway As
select trip.mode, 
       activity.type as acttype, 
       avg(trip.skim_travel_time)/60 as ttime_avg_skim, 
       avg(trip.routed_travel_time)/60 as ttime_avg_routed, 
       avg(trip.end - trip.start)/60 as ttime_avg, 
       avg(trip.travel_distance)/1000 as dist_avg, 
       4.0*count(*) as count
from trip, person, household, activity
where trip.person = person.person 
and person.household = household.household 
and person.age > 16 
and activity.trip = trip.trip_id 
and travel_distance < 1000000 
and trip.end - trip.start >= 0
and trip.skim_travel_time >= 0
and trip.skim_travel_time < 86400
and trip.mode in (0,9)
group by trip.mode, ACTTYPE"
6,7.390975952148438e-05,DROP TABLE IF EXISTS work_straight_line_dist_Average
7,0.02527141571044922,"CREATE TABLE IF NOT EXISTS work_straight_line_dist_Average As
SELECT avg(sqrt(pow(work_loc.x - home_loc.x, 2.0) + pow(work_loc.y - home_loc.y, 2.0))) / 1000 as dist_avg, 
       4.0*count(*) as count
FROM person, household, a.location as home_loc, a.location as work_loc
WHERE person.household = household.household
and household.location = home_loc.location
and person.work_location_id = work_loc.location
and home_loc.location <> work_loc.location"

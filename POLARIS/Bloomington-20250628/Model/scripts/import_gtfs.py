import os
from os.path import dirname, abspath, join
from polarislib.network.network import Network
import pandas as pd


def import_gtfs(model: Network, do_map_match=True):
    root = join(dirname(dirname(abspath(__file__))), 'supply', 'gtfs')
    max_speeds = pd.read_csv(join(root, 'transit_max_speeds.csv'))

    transit = model.transit
    transit.purge(False)

    feed = transit.new_gtfs(file_path=join(root, '2019-04-20.zip'), description='Connect Transit', agency='CONNECT')

    feed.set_allow_map_match(do_map_match)
    feed.set_maximum_speeds(max_speeds)
    feed.set_date('2019-04-25')
    feed.set_do_raw_shapes(True)
    feed.path_store.threshold=25000
    feed.doWork()

    transit.fix_connections_table()
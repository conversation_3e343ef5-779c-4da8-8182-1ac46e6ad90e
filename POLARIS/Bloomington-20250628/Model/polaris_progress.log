2025-07-01 09:49:49,940 Thread: 128321252776000 | [  INFO] Successfully initialized logging from property file: ./log/log4cpp.property
2025-07-01 09:49:49,940 Thread: 128321252776000 | [NOTICE] Attempting to load libLicenseHandler.so
2025-07-01 09:49:49,940 Thread: 128321252776000 | [ ERROR] 
	RUNTIME_ERROR at /home/<USER>/builds/polaris/code/polaris-linux/libs/core/DllSoHandler.h:97
	Message: Unable to load the dynamic library: libLicenseHandler.so, ERROR: libLicenseHandler.so: cannot open shared object file: No such file or directory


2025-07-01 09:49:49,940 Thread: 128321252776000 | [  INFO] Turned off signal handler
2025-07-01 09:49:49,940 Thread: 128321252776000 | [ ERROR] Stack trace:
2025-07-01 09:49:49,942 Thread: 128321252776000 | [ ERROR] 1   0x58ebea003efe + 0x58ebea003efe : (null)
2   0x58ebea004cd2 + 0x58ebea004cd2 : (null)
3   0x58ebea004fab + 0xdb : LicenseHandler::LicenseHandler()
4   0x58ebea005075 + 0x35 : LicenseHandler::Instance()
5   0x58ebea00541d + 0x2d : LicenseHandler::Checkout()
6   0x58ebe9ffcdb9 + 0x719 : polaris::Polaris_Logging_Interface::initialize_logger(std::filesystem::__cxx11::path, std::filesystem::__cxx11::path, bool)
7   0x58ebe9b3ad8e + 0x7e : main
8   0x74b51b029d90 + 0x74b51b029d90 : (null)
9   0x74b51b029e40 + 0x80 : __libc_start_main
10  0x58ebe9b3f195 + 0x25 : _start


2025-07-01 09:09:31,595 Thread: 129781864142912 | [  INFO] Successfully initialized logging from property file: ./log/log4cpp.property
2025-07-01 09:09:31,595 Thread: 129781864142912 | [NOTICE] Attempting to load libLicenseHandler.so
2025-07-01 09:09:31,595 Thread: 129781864142912 | [ ERROR] 
	RUNTIME_ERROR at /home/<USER>/builds/polaris/code/polaris-linux/libs/core/DllSoHandler.h:97
	Message: Unable to load the dynamic library: libLicenseHandler.so, ERROR: libLicenseHandler.so: cannot open shared object file: No such file or directory


2025-07-01 09:09:31,595 Thread: 129781864142912 | [  INFO] Turned off signal handler
2025-07-01 09:09:31,595 Thread: 129781864142912 | [ ERROR] Stack trace:
2025-07-01 09:09:31,597 Thread: 129781864142912 | [ ERROR] 1   0x5c4e71095efe + 0x5c4e71095efe : (null)
2   0x5c4e71096cd2 + 0x5c4e71096cd2 : (null)
3   0x5c4e71096fab + 0xdb : LicenseHandler::LicenseHandler()
4   0x5c4e71097075 + 0x35 : LicenseHandler::Instance()
5   0x5c4e7109741d + 0x2d : LicenseHandler::Checkout()
6   0x5c4e7108edb9 + 0x719 : polaris::Polaris_Logging_Interface::initialize_logger(std::filesystem::__cxx11::path, std::filesystem::__cxx11::path, bool)
7   0x5c4e70bccd8e + 0x7e : main
8   0x76092e429d90 + 0x76092e429d90 : (null)
9   0x76092e429e40 + 0x80 : __libc_start_main
10  0x5c4e70bd1195 + 0x25 : _start


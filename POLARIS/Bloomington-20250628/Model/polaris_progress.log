2025-07-01 09:53:58,437 Thread: 130598474086464 | [  INFO] Successfully initialized logging from property file: ./log/log4cpp.property
2025-07-01 09:53:58,437 Thread: 130598474086464 | [NOTICE] Attempting to load libLicenseHandler.so
2025-07-01 09:53:58,437 Thread: 130598474086464 | [ ERROR] 
	RUNTIME_ERROR at /home/<USER>/builds/polaris/code/polaris-linux/libs/core/DllSoHandler.h:97
	Message: Unable to load the dynamic library: libLicenseHandler.so, ERROR: libLicenseHandler.so: cannot open shared object file: No such file or directory


2025-07-01 09:53:58,437 Thread: 130598474086464 | [  INFO] Turned off signal handler
2025-07-01 09:53:58,437 Thread: 130598474086464 | [ ERROR] Stack trace:
2025-07-01 09:53:58,441 Thread: 130598474086464 | [ ERROR] 1   0x61821d125efe + 0x61821d125efe : (null)
2   0x61821d126cd2 + 0x61821d126cd2 : (null)
3   0x61821d126fab + 0xdb : LicenseHandler::LicenseHandler()
4   0x61821d127075 + 0x35 : LicenseHandler::Instance()
5   0x61821d12741d + 0x2d : LicenseHandler::Checkout()
6   0x61821d11edb9 + 0x719 : polaris::Polaris_Logging_Interface::initialize_logger(std::filesystem::__cxx11::path, std::filesystem::__cxx11::path, bool)
7   0x61821cc5cd8e + 0x7e : main
8   0x76c750029d90 + 0x76c750029d90 : (null)
9   0x76c750029e40 + 0x80 : __libc_start_main
10  0x61821cc61195 + 0x25 : _start


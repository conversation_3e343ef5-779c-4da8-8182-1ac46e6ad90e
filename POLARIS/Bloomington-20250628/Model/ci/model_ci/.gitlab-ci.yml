# image: ***************:5000/polarislib:linux_testing
image: localhost:5000/polarislib:linux_testing

stages:
  - build_test
  - deploy

compare_model_supply:
  stage: build_test
  variables:
    GIT_SSL_NO_VERIFY: "true"
  tags: [docker, linux, gl-runner]
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - echo "compare_model_supply - Pipeline source = ${CI_PIPELINE_SOURCE}, Branch = ${CI_COMMIT_BRANCH}"

    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git-out.gss.anl.gov/polaris/models/ci.git
    - . /venv-py311/bin/activate

    - source_file=$(ls -d "${CI_POLARIS_STUDIO_DIR}"/latest/*22.04*.whl | head -n1)
    - filename=$(basename "$source_file")
    - new_filename=${filename//_ubuntu_22.04_NO_LICENSE/}
    - cp "$source_file" "/tmp/$new_filename"
    - echo "${new_filename}"
    - uv pip install "/tmp/$new_filename"[builder]

    - \cp -r ./supply /tmp/new_supply
    
    - git fetch origin main
    - git checkout origin/main
    - python ./ci/model_ci/model_comparison.py
    - chmod u+x  ./ci/model_ci/message.sh
    - ./ci/model_ci/message.sh

    
build_and_test_model:
  tags: [docker, linux, gl-runner]
  stage: build_test
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - echo "build_and_test_model - Pipeline source = ${CI_PIPELINE_SOURCE}, Branch = ${CI_COMMIT_BRANCH}"

    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git-out.gss.anl.gov/polaris/models/ci.git
    - . /venv-py311/bin/activate

    - source_file=$(ls -d "${CI_POLARIS_STUDIO_DIR}"/latest/*22.04*.whl | head -n1)
    - filename=$(basename "$source_file")
    - new_filename=${filename//_ubuntu_22.04_NO_LICENSE/}
    - cp "$source_file" "/tmp/$new_filename"
    - echo "${new_filename}"
    - uv pip install "/tmp/$new_filename"[builder]

    - python ci/model_ci/tests.py "True"

build_and_deploy_model:
  tags: [docker, linux, gl-runner]
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE != "schedule"'
  script:
    - echo "build_and_deploy_model - Pipeline source = ${CI_PIPELINE_SOURCE}, Branch = ${CI_COMMIT_BRANCH}"
    
    # Clone this repo
    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git-out.gss.anl.gov/polaris/models/ci.git
    - . /venv-py311/bin/activate

    # Build the model (and run some criticality tests)
    - source_file=$(ls -d "${CI_POLARIS_STUDIO_DIR}"/latest/*22.04*.whl | head -n1)
    - filename=$(basename "$source_file")
    - new_filename=${filename//_ubuntu_22.04_NO_LICENSE/}
    - cp "$source_file" "/tmp/$new_filename"
    - echo "${new_filename}"
    - uv pip install "/tmp/$new_filename"[builder]

    - python ci/model_ci/tests.py "True"

    # Creates a record of the date and GIT/SHA that the model build corresponds to
    - my_date=${CI_PIPELINE_CREATED_AT:0:10}

    # Copies files to the file server
    - python ci/model_ci/copy_files.py  "/tmp/model_build/" "/mnt/publish/models/${CI_MODEL_NAME}"
    - echo "${my_date}-${CI_COMMIT_SHORT_SHA}" > "/mnt/publish/models/${CI_MODEL_NAME}/source.txt"
    
run_full_model:
  image: ***************:5000/polaris:model-runner
  stage: deploy
  tags: [model]
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || ($CI_COMMIT_BRANCH == "main" && $CI_MODEL_NAME == "Grid")'
  script:
    - echo "run_full_model - Pipeline source = ${CI_PIPELINE_SOURCE}, Branch = ${CI_COMMIT_BRANCH}"
    - if [[ "${PACKAGE_ON_WWW}" == "true" ]]; then
        echo "Packaging Demo!!!";
      else
        echo "${PACKAGE_ON_WWW}";
      fi

    # Clone this repo
    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git-out.gss.anl.gov/polaris/models/ci.git

    # Mount newcastle for getting previous json model metadata (docs_www) and mount VMS-FS for software artifacts
    - mkdir -p /mnt/docs_www /mnt/publish /mnt/demo_www
    - mount -t cifs -o username=${SVCUSERCI},password=${SVCPOLARISCI_PWD} //newcastle.egs.anl.gov/polaris/models/status /mnt/docs_www
    - mount -t cifs -o username=${SVCUSERCI},password=${SVCPOLARISCI_PWD} //newcastle.egs.anl.gov/polaris/models/ /mnt/demo_www
    - mount -t cifs -o username=${SVCUSERCI},password=${SVCPOLARISCI_PWD} //vms-fs.es.anl.gov/VMS/VMS_Software/15-CI-CD-Artifacts/Polaris /mnt/publish

    # Make a local copy of the model metadata folder in case of Samba connection issues over the 1-day runtime of the model
    - \cp -r /mnt/docs_www/ ./model_status


    # Install python deps
    - python3 -m venv /tmp/venv_model_run
    - source /tmp/venv_model_run/bin/activate

    - python3 -m pip install uv

    - source_file=$(ls -d "${CI_POLARIS_STUDIO_DIR}"/"${BRANCH_TO_TEST}"/*22.04*.whl | head -n1)
    - echo "${source_file}"
    - filename=$(basename "$source_file")
    - new_filename=${filename//_ubuntu_22.04_NO_LICENSE/}
    - cp "$source_file" "/tmp/$new_filename"
    - echo "${new_filename}"
    - python3 -m uv pip install "/tmp/$new_filename"[builder] anybadge
    
    # Run critical tests and then run the model to convergence
    - python3 ci/model_ci/tests.py  "False"
    - python3 ci/model_ci/model_run.py /tmp/model_build/ $(pwd)/model_status
    - if [[ "${PACKAGE_ON_WWW}" == "true" ]]; then
        python3 ci/model_ci/publish_demo.py /tmp/model_build $CI_MODEL_NAME $(pwd)/model_status;
      else
        echo "${PACKAGE_ON_WWW}";
      fi
  artifacts:
    paths:
      - ./model_status/*
    expire_in: 1 week

save_results_on_schedule:
  needs: [ run_full_model ]
  image: ubuntu:24.04
  stage: deploy
  tags: [model]
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || ($CI_COMMIT_BRANCH == "main" && $CI_MODEL_NAME == "Grid")'
  script:

    # Mount newcastle for persisting json model metadata (docs_www)
    - DEBIAN_FRONTEND=noninteractive apt update -y && apt install -y cifs-utils git
    
    # Clone this repo
    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git-out.gss.anl.gov/polaris/models/ci.git

    - mkdir -p /mnt/docs_www 
    - mount -t cifs -o username=${SVCUSERCI},password=${SVCPOLARISCI_PWD} //newcastle.egs.anl.gov/polaris/models/status /mnt/docs_www
    - \cp -r ./model_status/* /mnt/docs_www/
    - python3 ci/model_ci/assert_pipeline_result.py $(pwd)/model_status

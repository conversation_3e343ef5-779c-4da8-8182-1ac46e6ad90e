from pathlib import Path
import json

from polaris.utils.testing.model_comparison.compare_table_dumps import compare_table_dumps

def compare_models():
    report = compare_table_dumps(Path(__file__).parent.parent.parent / "supply", "/tmp/new_supply")
    with open(Path(__file__).parent.parent.parent / "model_diff.json", "w") as f:
        f.write(json.dumps({"body": "\n".join(report)}))


if __name__ == "__main__":
    compare_models()
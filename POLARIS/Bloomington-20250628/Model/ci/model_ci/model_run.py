import sys
import os
import json
import shutil
from datetime import datetime, timezone
from os.path import join, exists
from pathlib import Path
from copy_files import copy_to_server, copy_badge

from anybadge import Badge

from polaris.runs.convergence.config.convergence_config import ConvergenceConfig
from polaris.runs.convergence.convergence_runner import run_polaris_convergence
from polaris.utils.dir_utils import mkdir_p


def model_runner(model_path, status_path):
    # sourcery skip: aware-datetime-for-utc, do-not-use-bare-except
    model_name = os.environ["CI_MODEL_NAME"]
    config = ConvergenceConfig().from_file(join(model_path, "convergence_control.yaml"))

    # We check if model skims are available to start from them
    skim_dir = Path(os.environ["CI_MODELS_DIR"]) / "SKIMS" / model_name
    hwy_skim = skim_dir / "highway_skim_file.omx"
    pt_skim = skim_dir / "transit_skim_file.omx"

    print("Looking for skims", hwy_skim, pt_skim)
    if exists(hwy_skim) and exists(pt_skim):
        print("Found them!!!")
        shutil.copyfile(hwy_skim, join(model_path, "highway_skim_file.omx"))
        shutil.copyfile(pt_skim, join(model_path, "transit_skim_file.omx"))
        config.do_skim = False

    data_pth = f"{status_path}/{model_name.lower()}.json"
    badge_pth = f"{status_path}/{model_name.lower()}.svg"
    model_data = {"name": model_name}
    if os.path.isfile(data_pth):
        with open(data_pth, "r") as f:
            model_data = json.load(f)

    shutil.rmtree(status_path)
    mkdir_p(status_path)

    right_now = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%MZ")
    model_data["last_run"] = right_now
    color, success = ("green", "SUCCESS")
    out_folder = join(os.environ["CI_MODELS_DIR"], "RUN", model_name)

    polaris_path = config.polaris_exe.parent

    print("Copying cplex binaries into the executable folder")
    exe_dir = Path(os.environ["CI_POLARIS_LINUX_DIR"]) / "latest" / "ubuntu-22.04"
    for file in exe_dir.glob("*cplex*"):
        polaris_path.mkdir(parents=True, exist_ok=True)
        shutil.copy(file, polaris_path / file.name)

    vars = {"CI_COMMIT_REF_NAME": None, "CI": None}
    try:
        for var in vars.keys():
            vars[var] = os.environ.get(var)
            if vars[var] is not None:
                os.environ.pop(var)
    
        config.num_retries = 0
        run_polaris_convergence(config)
        model_data["status"] = "PASSED"
        model_data["last_succeed"] = right_now

        for var, val in vars.items():
            if val is not None:
                os.environ[val] = var

    except Exception:
        out_folder = join(os.environ["CI_MODELS_DIR"], "FAILED", model_name)
        model_data["status"] = "FAILED"
        model_data["last_succeed"] = model_data.get("last_succeed", "NEVER")
        color, success = ("red", "FAILED")
    finally:
        print(model_data)
        badge = Badge(right_now, default_color=color, value=success)
        badge.write_badge(badge_pth, overwrite=True)

        with open(data_pth, "w") as f:
            f.write(json.dumps(model_data, indent=3))

    copy_badge(Path(badge_pth), Path(data_pth))
    copy_to_server(model_path, out_folder)


if __name__ == "__main__":
    model_runner(sys.argv[1], sys.argv[2])

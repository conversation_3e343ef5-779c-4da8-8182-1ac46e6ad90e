# Copyright (c) 2025, UChicago Argonne, LLC
# BSD OPEN SOURCE LICENSE. Full license can be found in LICENSE.md
import json
import sys
import os
import shutil
from pathlib import Path
from datetime import datetime
from glob import glob

from polaris.utils.list_utils import first_and_only
import tarfile

def publish_demo_to_www(model_path: Path, city: str, status_path: Path):
    model_name = os.environ["CI_MODEL_NAME"]

    data_pth = f"{status_path}/{model_name.lower()}.json"

    with open(data_pth, 'r') as file:
        data_dict = json.load(file)
    
    if data_dict["status"] != "PASSED":
        print("MODEL RUN DID NOT RUN CORRECTLY, SO WE WILL NOT PUBLISH IT TO THE WEB")
        return

    try:
        release_date = datetime.today().strftime("%Y%m%d")
        demo_dir = Path(os.environ["CI_MODELS_DIR"]) / f"{city}-{release_date}"
        
        print(f"Creating Demo Directory at '{demo_dir}' and copying files to it")
        make_demo_dir(model_path, demo_dir)
        
        print(f"Compressing tarball {city.lower()}-demo-latest.tar.gz")
        tarball = demo_dir.parent / f"{city.lower()}-demo-latest.tar.gz"
        with tarfile.open(tarball, "w:gz") as tar:
            tar.add(demo_dir, arcname=os.path.basename(demo_dir))
        
        
        print("Copying to //newcastle.egs.anl.gov/polaris/models/")
        www = Path("/mnt/demo_www")
        shutil.copyfile(tarball, www / tarball.name)
    except Exception as e:
        print(f"Failed to copy the demo: {e}")


# Clean up existing directory and copy City model
def make_demo_dir(model_path: Path, demo_dir: Path) -> Path:
    shutil.rmtree(demo_dir, ignore_errors=True)
    if demo_dir.exists():
        print(f"Failed to remove {demo_dir}")
    demo_dir.mkdir(exist_ok=True, parents=True)
    shutil.copytree(model_path, demo_dir / "Model")

    # Copy the PPT containing a light introduction to getting started
    shutil.copy("ci/docs/Setup and Run Polaris.pptx", demo_dir / "Setup and Run Polaris.pptx")


# Copy Python wheel
def package_python_whl(demo_dir: Path) -> Path:
    wheel_dir = "/mnt/p/VMS_Software/15-CI-CD-Artifacts/Polaris/polaris-studio/wheels/latest"
    latest = first_and_only([i for i in list(glob(f"{wheel_dir}/*py3-none-win_amd64.whl")) if "NO_LICENSE" not in i])
    (demo_dir / "Binaries").mkdir(exist_ok=True)
    shutil.copy(latest, demo_dir / "Binaries" / latest.name)
    return demo_dir / "Binaries" / latest.name


if __name__ == "__main__":
    model_dir = sys.argv[1]
    model_name = sys.argv[2]
    status_dir = sys.argv[3]
    publish_demo_to_www(Path(model_dir), model_name, Path(status_dir))

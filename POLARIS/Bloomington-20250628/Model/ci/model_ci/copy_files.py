import sys
import os
from time import sleep
import shutil
import traceback
from os.path import isdir
from uuid import uuid4
from pathlib import Path


def copy_badge(badge_path: Path, data_path: Path):
    # Before copying the model, we copy the badge
    try:
        www = Path("/mnt/docs_www/")
        shutil.copyfile(badge_path, www / badge_path.name)
        shutil.copyfile(data_path, www / data_path.name)
    except:
        print("Failed to copy the badge and data")

def delete_empty_dirs(directory):
    # Recursively check all subdirectories
    for dirpath, dirnames, files in os.walk(directory, topdown=False):
        if not dirnames and not files:
            # This directory is empty, try to remove it
            try:
                os.rmdir(dirpath)
                print(f'Removed empty directory: {dirpath}')
            except OSError as e:
                print(f'Error removing {dirpath}: {e}')

def copy_to_server(source: str, destination: str):
    temp_dest = destination + "_" + str(uuid4().hex[:10])

    # First we try to copy to the file server 10 times
    delete_empty_dirs(source)
    success = False
    for _ in range(10):
        try:
            shutil.copytree(source, temp_dest, dirs_exist_ok=True, copy_function=shutil.copy)
            success = True
            break
        except:
            print(traceback.format_exc())
        sleep(2)

    if not success:
        print("Could not copy to the file server")
        return

    # Then we move the files to their final destination
    success = False
    for _ in range(10):
        try:
            if isdir(destination):
                shutil.rmtree(destination)
            shutil.move(temp_dest, destination)
            success = True
            break
        except:
            print(traceback.format_exc())
        sleep(2)

    if not success:
        print("Could not copy new model run to its final destination in the file server")
        print(f"Latest model results are in folder: {temp_dest}")

    # # Finally, we remove the temporary folder from the server
    # for _ in range(10):
    #     try:
    #         if isdir(temp_dest):
    #             shutil.rmtree(temp_dest)
    #             break
    #     except:
    #         print(traceback.format_exc())
    #         sleep(2)

    # if isdir(temp_dest):
    #     print("Could not remove the temporary folder from the server. This will have to be manually cleaned.")


if __name__ == "__main__":
    copy_to_server(sys.argv[1], sys.argv[2])

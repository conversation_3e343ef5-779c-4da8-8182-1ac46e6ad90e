# Testing models

Models maintained in this Gitlab group can take advantage of standardized procedures for testing and building.

There are three types of testing available for models maintained with our current infrastructure.

## On merge requests

Any time a merge request for a model is created, we use the Polaris-Studio to build
the model and run critical tests against.

:::{admonition} When running this test...
	 we use the **LATEST AND GREATEST** version of Polaris-Studio

After these tests are run, we use a model-comparison procedure from Polaris-Studio to summarize  
any differences in the supply files from the model. This allows for meaningful analysis of changes 
to the supply model, particularly when wholesale changes are done to specific columns in large tables.
 The result of this analysis is reported as a comment on the pull request, as exemplified below.

![model structure](./docs/ci_model_bot.png)


## Continuous integration - Model builds

For models that are actively being maintained, it is also possible to deploy integration testing against
new versions of Polaris-Studio.

This test has two objectives:

* Create a new version of the model with all upgrades allready applied to it
* Make evident and required modifications to model, as that would cause this test to break


## Continuous integration - Model runs on a schedule

The most comprehensive Integration testing available is the performance of full model runs frequently.
This is an extremely resource-intensive task, and therefore cannot be run for every new merge into 
Polaris-Studio or Polaris itself.

Instead, these tests are set to be run on a schedule, and results of runs are copied to the appropriate
folders on the file server in both cases.

:::{admonition} When running this test...
During the preparation of Polaris releases, the group CI variable **BRANCH_TO_TEST** is changed to the
appropriate release branch and all full model runs are done against that branch.

Model Status can be found [ONLINE](https://polaris.taps.anl.gov/polaris-studio/models/index.html)


# Setting up testing for a new model repository

The first thing to do when setting up testing for a new model is to setup an environment variable named  **CI_MODEL_NAME** in the 
repository configurations where its value is the name of the model that the repository holds (i.e. Grid, Chicago, Austin),
as shown below.

![model structure](./docs/ci_setting_variable.png)


## CI pipeline file

The second step in setting up CI on a new model is to create a text file with the name **.gitlab-ci.yml** 
in the root of the model repository with the following contents:

    include:
	  - project: polaris/models/ci
		ref: main
		file: /model_ci/.gitlab-ci.yml


## Integration testing


### Setting Polaris-Studio integration testing

Setting automated model builds at any new version of Polaris-Studio must be done on the Polaris-Studio 
repository on a [CI file](https://git-out.gss.anl.gov/polaris/code/polarislib/-/blob/main/ci/testing_models_ci.yml?ref_type=heads).

It consists of simply creating one more job, using the existing ones as template, as shown below.

![CI on Polaris-Studio](./docs/ci_model_build.png)


### Setting full model run integration testing

Before setting the model run schedule, it is critical to change the pipeline timeout to something suitable, like 1 day (1d).
This option can be found under Settings-CI/CD-General, and is shown below.

![CI set pipeline timeout](./docs/ci_pipeline_timeout.png)

With the timeout changed to something suitable, it is possible to set the schedule as below.

![CI Polaris-Studio](./docs/ci_set_schedule.png)

There are multiple models being continuously run on vbery sparse infrastructure, so it is necessary to 
find out what are the days/schedules where other models are being run in order to not create excessive long lines.


## Customizing test pipelines

If a model requires more testing than others, the user has the option of adding new jobs into the ci file.


In that
case, the **.gitlab-ci.yml** file could look something like the following

    include:
	  - project: polaris/models/ci
		ref: main
		file: /model_ci/.gitlab-ci.yml

    custom_test:
	  stage: build_test	
	  tags:
		- docker
		- linux
	  script:
		- apt-get update
		- DEBIAN_FRONTEND=noninteractive apt-get install -y libsqlite3-mod-spatialite libspatialite-dev
		- ln -s /usr/lib/x86_64-linux-gnu/mod_spatialite.so /usr/lib/x86_64-linux-gnu/mod_spatialite
		- wheel_list=$(ls -d "${CI_POLARISLIB_DIR}"/*.whl*)
		- python -m pip install "${wheel_list}"
		- python my_custom_python.py

## Continuous model run schedule

All models are run at least once a week for a number of iterations that guarantees that the total run time 
will be smaller than 21h. The other 3h of the day are set aside to run a daily Bloomington (single iteration) that gives us a canary in the coal mine for common failures. 


schedule: 

All other models run at 0:00 GMT.

* Austin:        0  0 * * 0 (Sunday)
   
* Honolulu:      0  0 * * 1 (Sunday)
   
* Chicago:       0  0 * * 2 (Tuesday)
   
* Detroit:       0  0 * * 3 (Wednesday)
   
* Los Angeles:   0  0 * * 4 (Thursday)
   
* DFW:           0  0 * * 5 (Friday)
   
* Bloomington:   0  0 * * 6 (Saturday)


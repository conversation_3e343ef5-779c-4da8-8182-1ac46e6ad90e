# log4cpp.property

# The following is an example file showing the default setup of POLARIS 
# It can be copied into the log folder of a model to override the logging
# behaviour on runs of that model
#   i.e. <project_dir>/log/log4cpp.property
# Note that by default the progress log includes INFO level messages, but 
# the below includes examples of how to create a separate debug log file if 
# your situation warrants such.

log4j.rootCategory=INFO, coutAppender, logFileAppender


log4j.appender.logFileAppender=RollingFileAppender
log4j.appender.logFileAppender.additive=false
# log4j.appender.logFileAppender.threshold=NOTICE # <- This doesn't work and will always output whatever
#                                                 #    the level of the root category is
log4j.appender.logFileAppender.append=false
log4j.appender.logFileAppender.fileName=polaris_progress.log
log4j.appender.logFileAppender.MaxFileSize=10MB
log4j.appender.logFileAppender.maxBackupIndex=50
log4j.appender.logFileAppender.layout=PatternLayout
log4j.appender.logFileAppender.layout.ConversionPattern=%d{ISO8601} Thread: %t | [%6p] %m%n

log4j.appender.coutAppender=ConsoleAppender
log4j.appender.coutAppender.threshold=NOTICE     # <- This does work - not sure why ConsoleAppender does but FileAppenders dont
log4j.appender.coutAppender.target=stdout
log4j.appender.coutAppender.layout=PatternLayout
log4j.appender.coutAppender.layout.ConversionPattern=%d{ISO8601} Thread: %t | [%6p] %m%n

### DOCUMENTATION ###

# CATEGORY
# rootCategory must be set, other categories must derive from root or from one of root's derived categories, (ex. log4cpp.category.sub1=DEBUG, coutAppender)
# First setting is priority (lower settings include all higher settings):  
#   EMERG
#   FATAL
#   ALERT
#   CRIT
#   ERROR
#   WARN
#   NOTICE
#   INFO
#   DEBUG
#   NOTSET

# You can also change the additivity for each category. TRUE means it inherits all prior appenders, FALSE means that it only uses the last one.
# category.debug=DEBUG, debugAppender
# additivity.debug=false

# Second setting is the list of appenders (created in this file), separated by commas, (ex. log4cpp.rootCategory=DEBUG, logFileAppender, coutAppender)

# APPENDER
# Appenders are defined with a name and type
# ex. name "logFileAppender" type "FileAppender"

# The following types are available:

# ConsoleAppender: Outputs to console window. 
	# Set variable "target" to either stdout or stderr
	
# FileAppender: Outputs to a file.
	# Set variable "fileName" to target file (ex. ./LOG_FILE_HERE.log).
	# Set variable "append" to either true or false (append existing file (true) or overwrite (false))
	
# RollingFileAppender:
	# Set variable "fileName" to target file (ex. ./LOG_FILE_HERE.log).
	# Set variable "append" to either true or false (append existing file (true) or overwrite (false))
	# Set the variable "maxFileSize" to change the maximum size of a log file before it rolls over (in bytes!)
	# Set the variable "maxBackupIndex" to change the maximum number of log files to keep when rolling over
	
#%GW : I don't really know what the appenders below do
# DailyRollingFileAppender:
	# Set variable "fileName" to target file (ex. ./LOG_FILE_HERE.log).
	# Set variable "append" to either true or false (append existing file (true) or overwrite (false))
	
# SyslogAppender: DISABLED
# LocalSyslogAppender: DISABLED
# AbortAppender:
# IdsaAppender: DISABLED
# Win32DebugAppender:
# NTEventLogAppender:

# All Appenders have a "layout" variable that changes the output of the file.
# SimpleLayout prints "priority - message" (%p - %m)
# BasicLayout prints "timeStamp priority category ndc: message"

# PatternLayout allows you to create your own format.
	# PatternLayout supports following set of format characters:
		# %% - a single percent sign
		# %c - the category
		# %d - the date\n Date format: The date format character may be followed by a date format specifier enclosed between braces. For example, %d{%H:%M:%S,%l} or %d{%d %m %Y %H:%M:%S,%l}. 
			# If no date format specifier is given then the following format is used: "Wed Jan 02 02:03:55 1980". The date format specifier admits the same syntax as the ANSI C function strftime, 
			# with 1 addition. The addition is the specifier %l for milliseconds, padded with zeros to make 3 digits.
		# %m - the message
		# %n - the platform specific line separator
		# %p - the priority
		# %r - milliseconds since this layout was created.
		# %R - seconds since Jan 1, 1970
		# %u - clock ticks since process start
		# %x - the NDC
		# %t - thread name
	# By default, ConversionPattern for PatternLayout is set to "%m%n".
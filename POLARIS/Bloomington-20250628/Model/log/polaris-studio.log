2025-06-28 00:03:50 UTC+0000 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-06-28 00:03:51 UTC+0000 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-06-28 00:03:51 UTC+0000 - The Supply file has no additional consistency issues
2025-06-28 00:03:51 UTC+0000 - Setting backups...
2025-06-28 00:03:51 UTC+0000 - Running transit summary sql file: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/runs/sql/transit_stats.sql
2025-06-28 00:03:51 UTC+0000 - 
2025-06-28 00:03:51 UTC+0000 - Starting iteration: 00_skim_iteration
2025-06-28 00:03:51 UTC+0000 -   Cleaning database for ABM
2025-06-28 00:03:51 UTC+0000 -   Running Polaris
2025-06-28 00:03:51 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 00:03:51 UTC+0000 -     time_dependent_routing               : False
2025-06-28 00:03:51 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 00:03:51 UTC+0000 -     percent_to_synthesize                : 0.0
2025-06-28 00:03:51 UTC+0000 -     read_trip_factors                    : {'External': 0.0}
2025-06-28 00:03:51 UTC+0000 -     read_population_from_database        : False
2025-06-28 00:03:51 UTC+0000 -     replan                               : {}
2025-06-28 00:03:51 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.0
2025-06-28 00:03:51 UTC+0000 -     skim_nodes_per_zone                  : 4
2025-06-28 00:03:51 UTC+0000 -     read_skim_tables                     : False
2025-06-28 00:03:51 UTC+0000 -     write_skim_tables                    : True
2025-06-28 00:03:51 UTC+0000 -     generate_transit_skims               : True
2025-06-28 00:03:51 UTC+0000 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-06-28 00:03:51 UTC+0000 -     EV_charging                          : False
2025-06-28 00:03:51 UTC+0000 -     use_tnc_system                       : True
2025-06-28 00:03:51 UTC+0000 -     tnc_feedback                         : False
2025-06-28 00:03:51 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 00:03:51 UTC+0000 -     use_freight_model                    : False
2025-06-28 00:03:51 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 00:03:51 UTC+0000 -     output_directory                     : Bloomington_00_skim_iteration
2025-06-28 00:03:51 UTC+0000 -     database_name                        : Bloomington
2025-06-28 00:03:51 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 00:03:51 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 00:03:51 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 00:03:51 UTC+0000 -       arg2: 20
2025-06-28 00:03:51 UTC+0000 -        dir: /tmp/model_build
2025-06-28 00:03:51 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 01:02:36 UTC+0000 -   Running Polaris: Done in 0:58:44.268048 seconds
2025-06-28 01:02:36 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_00_skim_iteration
2025-06-28 01:02:36 UTC+0000 -   Default End of Loop Function
2025-06-28 01:02:36 UTC+0000 -     skim iteration - copying back highway skim file
2025-06-28 01:02:36 UTC+0000 -     skim iteration - copying back transit skim files
2025-06-28 01:02:36 UTC+0000 -     finished copying back files
2025-06-28 01:02:36 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 01:02:36 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 01:02:36 UTC+0000 -         ~ RUNNING in background thread for iteration 00_skim_iteration
2025-06-28 01:02:36 UTC+0000 - 
2025-06-28 01:02:36 UTC+0000 -         ~   Running WTF analysis
2025-06-28 01:02:36 UTC+0000 - Starting iteration: 01_abm_init_iteration
2025-06-28 01:02:36 UTC+0000 -   Cleaning database for ABM
2025-06-28 01:02:36 UTC+0000 -   Running Polaris
2025-06-28 01:02:36 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 01:02:36 UTC+0000 -     time_dependent_routing               : False
2025-06-28 01:02:36 UTC+0000 -     read_population_from_database        : False
2025-06-28 01:02:36 UTC+0000 -     percent_to_synthesize                : 0.25
2025-06-28 01:02:36 UTC+0000 -     read_trip_factors                    : {'External': 0.25}
2025-06-28 01:02:36 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 01:02:36 UTC+0000 -     tnc_feedback                         : False
2025-06-28 01:02:36 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.04
2025-06-28 01:02:36 UTC+0000 -     use_freight_model                    : False
2025-06-28 01:02:36 UTC+0000 -     model_b2c_delivery                   : False
2025-06-28 01:02:36 UTC+0000 -     model_b2b_delivery                   : False
2025-06-28 01:02:36 UTC+0000 -     synthesize_b2b_demand                : False
2025-06-28 01:02:36 UTC+0000 -     chain_fixed_freight_trips            : True
2025-06-28 01:02:36 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 01:02:36 UTC+0000 -     output_directory                     : Bloomington_01_abm_init_iteration
2025-06-28 01:02:36 UTC+0000 -     database_name                        : Bloomington
2025-06-28 01:02:36 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 01:02:36 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 01:02:36 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 01:02:36 UTC+0000 -       arg2: 20
2025-06-28 01:02:36 UTC+0000 -        dir: /tmp/model_build
2025-06-28 01:02:36 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 01:02:37 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 01:02:37 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 01:02:37 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:00.625865 seconds
2025-06-28 01:02:37 UTC+0000 -         ~   Running Charging analysis
2025-06-28 01:02:37 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.065170 seconds
2025-06-28 01:02:37 UTC+0000 - Table vmt_vht_by_mode does not exist in the source database - skipping
2025-06-28 01:02:37 UTC+0000 -         ~   Compressing files
2025-06-28 01:02:37 UTC+0000 -         ~     Compressing ['Bloomington-Result.sqlite', 'Bloomington-Demand.sqlite', 'Bloomington-Supply.sqlite'] in dir /tmp/model_build/Bloomington_00_skim_iteration
2025-06-28 01:02:37 UTC+0000 -         ~       - Bloomington-Result.sqlite
2025-06-28 01:02:37 UTC+0000 -         ~       - Bloomington-Demand.sqlite
2025-06-28 01:02:37 UTC+0000 -         ~       - Bloomington-Supply.sqlite
2025-06-28 01:02:37 UTC+0000 -         ~   Checking that compressed files were created cleanly
2025-06-28 01:02:37 UTC+0000 -         ~   All tar.gz files pass sanity checking
2025-06-28 01:02:37 UTC+0000 -         ~   Deleting files which were compressed
2025-06-28 01:02:37 UTC+0000 -         ~   Compressing files: Done in 0:00:00.260748 seconds
2025-06-28 01:02:37 UTC+0000 -         ~ RUNNING in background thread for iteration 00_skim_iteration: Done in 0:00:01.018719 seconds
2025-06-28 02:16:08 UTC+0000 -   Running Polaris: Done in 1:13:31.312703 seconds
2025-06-28 02:16:08 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_01_abm_init_iteration
2025-06-28 02:16:08 UTC+0000 -   Default End of Loop Function
2025-06-28 02:16:08 UTC+0000 -     abm_init iteration - copying back highway skim file
2025-06-28 02:16:08 UTC+0000 -     abm_init iteration - copying back demand db
2025-06-28 02:16:08 UTC+0000 -     abm_init iteration - copying back result db
2025-06-28 02:16:08 UTC+0000 -     finished copying back files
2025-06-28 02:16:08 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 02:16:08 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 02:16:08 UTC+0000 -         ~ RUNNING in background thread for iteration 01_abm_init_iteration
2025-06-28 02:16:08 UTC+0000 - 
2025-06-28 02:16:08 UTC+0000 -         ~   Running WTF analysis
2025-06-28 02:16:08 UTC+0000 - Starting iteration: iteration_1
2025-06-28 02:16:08 UTC+0000 -   Cleaning database for ABM
2025-06-28 02:16:09 UTC+0000 -   Running Polaris
2025-06-28 02:16:09 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 02:16:09 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 02:16:09 UTC+0000 -     percent_to_synthesize                : 1.0
2025-06-28 02:16:09 UTC+0000 -     read_trip_factors                    : {'External': 1.0, 'ABM': 0.0}
2025-06-28 02:16:09 UTC+0000 -     read_population_from_database        : True
2025-06-28 02:16:09 UTC+0000 -     workplaces                           : False
2025-06-28 02:16:09 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.04
2025-06-28 02:16:09 UTC+0000 -     use_freight_model                    : False
2025-06-28 02:16:09 UTC+0000 -     model_b2c_delivery                   : False
2025-06-28 02:16:09 UTC+0000 -     model_b2b_delivery                   : False
2025-06-28 02:16:09 UTC+0000 -     synthesize_b2b_demand                : False
2025-06-28 02:16:09 UTC+0000 -     chain_fixed_freight_trips            : True
2025-06-28 02:16:09 UTC+0000 -     time_dependent_routing               : True
2025-06-28 02:16:09 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 02:16:09 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 02:16:09 UTC+0000 -     output_directory                     : Bloomington_iteration_1
2025-06-28 02:16:09 UTC+0000 -     database_name                        : Bloomington
2025-06-28 02:16:09 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 02:16:09 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 02:16:09 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 02:16:09 UTC+0000 -       arg2: 20
2025-06-28 02:16:09 UTC+0000 -        dir: /tmp/model_build
2025-06-28 02:16:09 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 02:16:12 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 02:16:12 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 02:16:12 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:03.669478 seconds
2025-06-28 02:16:12 UTC+0000 -         ~   Generating Gap Reports
2025-06-28 02:16:12 UTC+0000 -             - /tmp/model_build/Bloomington_01_abm_init_iteration
2025-06-28 02:16:12 UTC+0000 -         ~   Generating Gap Reports: Done in 0:00:00.322061 seconds
2025-06-28 02:16:12 UTC+0000 -         ~   Running Charging analysis
2025-06-28 02:16:13 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.488315 seconds
2025-06-28 02:16:13 UTC+0000 -         ~   Pre-caching kpis
2025-06-28 02:16:36 UTC+0000 - Can't find a file matching pattern: Freight.sqlite
2025-06-28 02:16:38 UTC+0000 - No target and simulation data
2025-06-28 02:16:38 UTC+0000 - Saving precaching KPIs runtime report, total time: 23.5s
2025-06-28 02:16:38 UTC+0000 -         ~   Pre-caching kpis: Done in 0:00:24.755278 seconds
2025-06-28 02:16:38 UTC+0000 -         ~   Compressing files
2025-06-28 02:16:38 UTC+0000 -         ~     Compressing ['Bloomington-Result.sqlite', 'Bloomington-Demand.sqlite', 'Bloomington-Supply.sqlite'] in dir /tmp/model_build/Bloomington_01_abm_init_iteration
2025-06-28 02:16:38 UTC+0000 -         ~       - Bloomington-Result.sqlite
2025-06-28 02:16:38 UTC+0000 -         ~       - Bloomington-Demand.sqlite
2025-06-28 02:16:38 UTC+0000 -         ~       - Bloomington-Supply.sqlite
2025-06-28 02:16:38 UTC+0000 -         ~   Checking that compressed files were created cleanly
2025-06-28 02:16:38 UTC+0000 -         ~   All tar.gz files pass sanity checking
2025-06-28 02:16:38 UTC+0000 -         ~   Deleting files which were compressed
2025-06-28 02:16:38 UTC+0000 -         ~   Compressing files: Done in 0:00:00.408913 seconds
2025-06-28 02:16:38 UTC+0000 -         ~ RUNNING in background thread for iteration 01_abm_init_iteration: Done in 0:00:29.708902 seconds
2025-06-28 03:29:05 UTC+0000 -   Running Polaris: Done in 1:12:56.204754 seconds
2025-06-28 03:29:05 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_iteration_1
2025-06-28 03:29:05 UTC+0000 -   Default End of Loop Function
2025-06-28 03:29:05 UTC+0000 -     normal iteration - copying back highway skim file
2025-06-28 03:29:05 UTC+0000 -     normal iteration - copying back demand db
2025-06-28 03:29:05 UTC+0000 -     normal iteration - copying back result db
2025-06-28 03:29:05 UTC+0000 -     finished copying back files
2025-06-28 03:29:06 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 03:29:06 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 03:29:06 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_1
2025-06-28 03:29:06 UTC+0000 - 
2025-06-28 03:29:06 UTC+0000 -         ~   Running WTF analysis
2025-06-28 03:29:06 UTC+0000 - Starting iteration: iteration_2
2025-06-28 03:29:06 UTC+0000 -   Cleaning database for ABM
2025-06-28 03:29:06 UTC+0000 -   Running Polaris
2025-06-28 03:29:06 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 03:29:06 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 03:29:06 UTC+0000 -     percent_to_synthesize                : 1.0
2025-06-28 03:29:06 UTC+0000 -     read_trip_factors                    : {'External': 1.0, 'ABM': 0.0}
2025-06-28 03:29:06 UTC+0000 -     read_population_from_database        : True
2025-06-28 03:29:06 UTC+0000 -     workplaces                           : False
2025-06-28 03:29:06 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.04
2025-06-28 03:29:06 UTC+0000 -     use_freight_model                    : False
2025-06-28 03:29:06 UTC+0000 -     model_b2c_delivery                   : False
2025-06-28 03:29:06 UTC+0000 -     model_b2b_delivery                   : False
2025-06-28 03:29:06 UTC+0000 -     synthesize_b2b_demand                : False
2025-06-28 03:29:06 UTC+0000 -     chain_fixed_freight_trips            : True
2025-06-28 03:29:06 UTC+0000 -     time_dependent_routing               : True
2025-06-28 03:29:06 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 03:29:06 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 03:29:06 UTC+0000 -     output_directory                     : Bloomington_iteration_2
2025-06-28 03:29:06 UTC+0000 -     database_name                        : Bloomington
2025-06-28 03:29:06 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 03:29:06 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 03:29:06 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 03:29:06 UTC+0000 -       arg2: 20
2025-06-28 03:29:06 UTC+0000 -        dir: /tmp/model_build
2025-06-28 03:29:06 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 03:29:10 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 03:29:10 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 03:29:10 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:03.783160 seconds
2025-06-28 03:29:10 UTC+0000 -         ~   Generating Gap Reports
2025-06-28 03:29:10 UTC+0000 -             - /tmp/model_build/Bloomington_iteration_1
2025-06-28 03:29:10 UTC+0000 -         ~   Generating Gap Reports: Done in 0:00:00.333380 seconds
2025-06-28 03:29:10 UTC+0000 -         ~   Running Charging analysis
2025-06-28 03:29:11 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.529241 seconds
2025-06-28 03:29:11 UTC+0000 -         ~   Pre-caching kpis
2025-06-28 03:29:32 UTC+0000 - Can't find a file matching pattern: Freight.sqlite
2025-06-28 03:29:34 UTC+0000 - No target and simulation data
2025-06-28 03:29:34 UTC+0000 - Saving precaching KPIs runtime report, total time: 23.4s
2025-06-28 03:29:34 UTC+0000 -         ~   Pre-caching kpis: Done in 0:00:23.448899 seconds
2025-06-28 03:29:34 UTC+0000 -         ~   Compressing files
2025-06-28 03:29:34 UTC+0000 -         ~     Compressing ['Bloomington-Result.sqlite', 'Bloomington-Demand.sqlite', 'Bloomington-Supply.sqlite'] in dir /tmp/model_build/Bloomington_iteration_1
2025-06-28 03:29:34 UTC+0000 -         ~       - Bloomington-Result.sqlite
2025-06-28 03:29:34 UTC+0000 -         ~       - Bloomington-Demand.sqlite
2025-06-28 03:29:34 UTC+0000 -         ~       - Bloomington-Supply.sqlite
2025-06-28 03:29:34 UTC+0000 -         ~   Checking that compressed files were created cleanly
2025-06-28 03:29:34 UTC+0000 -         ~   All tar.gz files pass sanity checking
2025-06-28 03:29:34 UTC+0000 -         ~   Deleting files which were compressed
2025-06-28 03:29:34 UTC+0000 -         ~   Compressing files: Done in 0:00:00.407321 seconds
2025-06-28 03:29:34 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_1: Done in 0:00:28.567309 seconds
2025-06-28 04:43:33 UTC+0000 -   Running Polaris: Done in 1:14:26.295309 seconds
2025-06-28 04:43:33 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_iteration_2
2025-06-28 04:43:33 UTC+0000 -   Default End of Loop Function
2025-06-28 04:43:33 UTC+0000 -     normal iteration - copying back highway skim file
2025-06-28 04:43:33 UTC+0000 -     normal iteration - copying back demand db
2025-06-28 04:43:33 UTC+0000 -     normal iteration - copying back result db
2025-06-28 04:43:33 UTC+0000 -     finished copying back files
2025-06-28 04:43:34 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 04:43:34 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 04:43:34 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_2
2025-06-28 04:43:34 UTC+0000 - 
2025-06-28 04:43:34 UTC+0000 -         ~   Running WTF analysis
2025-06-28 04:43:34 UTC+0000 - Starting iteration: iteration_3
2025-06-28 04:43:34 UTC+0000 -   Cleaning database for ABM
2025-06-28 04:43:34 UTC+0000 -   Running Polaris
2025-06-28 04:43:34 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 04:43:34 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 04:43:34 UTC+0000 -     percent_to_synthesize                : 1.0
2025-06-28 04:43:34 UTC+0000 -     read_trip_factors                    : {'External': 1.0, 'ABM': 0.0}
2025-06-28 04:43:34 UTC+0000 -     read_population_from_database        : True
2025-06-28 04:43:34 UTC+0000 -     workplaces                           : False
2025-06-28 04:43:34 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.04
2025-06-28 04:43:34 UTC+0000 -     use_freight_model                    : False
2025-06-28 04:43:34 UTC+0000 -     model_b2c_delivery                   : False
2025-06-28 04:43:34 UTC+0000 -     model_b2b_delivery                   : False
2025-06-28 04:43:34 UTC+0000 -     synthesize_b2b_demand                : False
2025-06-28 04:43:34 UTC+0000 -     chain_fixed_freight_trips            : True
2025-06-28 04:43:34 UTC+0000 -     time_dependent_routing               : True
2025-06-28 04:43:34 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 04:43:34 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 04:43:34 UTC+0000 -     output_directory                     : Bloomington_iteration_3
2025-06-28 04:43:34 UTC+0000 -     database_name                        : Bloomington
2025-06-28 04:43:34 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 04:43:34 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 04:43:34 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 04:43:34 UTC+0000 -       arg2: 20
2025-06-28 04:43:34 UTC+0000 -        dir: /tmp/model_build
2025-06-28 04:43:34 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 04:43:38 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 04:43:38 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 04:43:38 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:04.090013 seconds
2025-06-28 04:43:38 UTC+0000 -         ~   Generating Gap Reports
2025-06-28 04:43:38 UTC+0000 -             - /tmp/model_build/Bloomington_iteration_2
2025-06-28 04:43:38 UTC+0000 -         ~   Generating Gap Reports: Done in 0:00:00.307033 seconds
2025-06-28 04:43:38 UTC+0000 -         ~   Running Charging analysis
2025-06-28 04:43:39 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.525267 seconds
2025-06-28 04:43:39 UTC+0000 -         ~   Pre-caching kpis
2025-06-28 04:44:00 UTC+0000 - Can't find a file matching pattern: Freight.sqlite
2025-06-28 04:44:02 UTC+0000 - No target and simulation data
2025-06-28 04:44:02 UTC+0000 - Saving precaching KPIs runtime report, total time: 23.4s
2025-06-28 04:44:02 UTC+0000 -         ~   Pre-caching kpis: Done in 0:00:23.427989 seconds
2025-06-28 04:44:02 UTC+0000 -         ~   Compressing files
2025-06-28 04:44:02 UTC+0000 -         ~     Compressing ['Bloomington-Result.sqlite', 'Bloomington-Demand.sqlite', 'Bloomington-Supply.sqlite'] in dir /tmp/model_build/Bloomington_iteration_2
2025-06-28 04:44:02 UTC+0000 -         ~       - Bloomington-Result.sqlite
2025-06-28 04:44:02 UTC+0000 -         ~       - Bloomington-Demand.sqlite
2025-06-28 04:44:02 UTC+0000 -         ~       - Bloomington-Supply.sqlite
2025-06-28 04:44:02 UTC+0000 -         ~   Checking that compressed files were created cleanly
2025-06-28 04:44:03 UTC+0000 -         ~   All tar.gz files pass sanity checking
2025-06-28 04:44:03 UTC+0000 -         ~   Deleting files which were compressed
2025-06-28 04:44:03 UTC+0000 -         ~   Compressing files: Done in 0:00:00.416396 seconds
2025-06-28 04:44:03 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_2: Done in 0:00:28.827821 seconds
2025-06-28 05:57:36 UTC+0000 -   Running Polaris: Done in 1:14:01.481022 seconds
2025-06-28 05:57:36 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_iteration_3
2025-06-28 05:57:36 UTC+0000 -   Default End of Loop Function
2025-06-28 05:57:36 UTC+0000 -     normal iteration - copying back highway skim file
2025-06-28 05:57:36 UTC+0000 -     normal iteration - copying back demand db
2025-06-28 05:57:36 UTC+0000 -     normal iteration - copying back result db
2025-06-28 05:57:36 UTC+0000 -     finished copying back files
2025-06-28 05:57:37 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 05:57:37 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 05:57:37 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_3
2025-06-28 05:57:37 UTC+0000 - 
2025-06-28 05:57:37 UTC+0000 -         ~   Running WTF analysis
2025-06-28 05:57:37 UTC+0000 - Starting iteration: iteration_4
2025-06-28 05:57:37 UTC+0000 -   Cleaning database for ABM
2025-06-28 05:57:38 UTC+0000 -   Running Polaris
2025-06-28 05:57:38 UTC+0000 -   The following modifications will be applied to /tmp/model_build/scenario_abm.json:
2025-06-28 05:57:38 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 05:57:38 UTC+0000 -     percent_to_synthesize                : 1.0
2025-06-28 05:57:38 UTC+0000 -     read_trip_factors                    : {'External': 1.0, 'ABM': 0.0}
2025-06-28 05:57:38 UTC+0000 -     read_population_from_database        : True
2025-06-28 05:57:38 UTC+0000 -     workplaces                           : False
2025-06-28 05:57:38 UTC+0000 -     vehicle_trajectory_sample_rate       : 0.04
2025-06-28 05:57:38 UTC+0000 -     use_freight_model                    : False
2025-06-28 05:57:38 UTC+0000 -     model_b2c_delivery                   : False
2025-06-28 05:57:38 UTC+0000 -     model_b2b_delivery                   : False
2025-06-28 05:57:38 UTC+0000 -     synthesize_b2b_demand                : False
2025-06-28 05:57:38 UTC+0000 -     chain_fixed_freight_trips            : True
2025-06-28 05:57:38 UTC+0000 -     time_dependent_routing               : True
2025-06-28 05:57:38 UTC+0000 -     time_dependent_routing_weight_factor : 1.0
2025-06-28 05:57:38 UTC+0000 -     write_lc_traffic_trajectory          : True
2025-06-28 05:57:38 UTC+0000 -     traffic_scale_factor                 : 0.25
2025-06-28 05:57:38 UTC+0000 -     output_directory                     : Bloomington_iteration_4
2025-06-28 05:57:38 UTC+0000 -     database_name                        : Bloomington
2025-06-28 05:57:38 UTC+0000 -     input_result_database_name           : Bloomington
2025-06-28 05:57:38 UTC+0000 -        exe: /tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model
2025-06-28 05:57:38 UTC+0000 -       arg1: /tmp/model_build/scenario_abm.modified.json
2025-06-28 05:57:38 UTC+0000 -       arg2: 20
2025-06-28 05:57:38 UTC+0000 -        dir: /tmp/model_build
2025-06-28 05:57:38 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', PosixPath('/tmp/model_build/scenario_abm.modified.json'), '20']
2025-06-28 05:57:41 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 05:57:41 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 05:57:41 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:03.845084 seconds
2025-06-28 05:57:41 UTC+0000 -         ~   Generating Gap Reports
2025-06-28 05:57:41 UTC+0000 -             - /tmp/model_build/Bloomington_iteration_3
2025-06-28 05:57:41 UTC+0000 -         ~   Generating Gap Reports: Done in 0:00:00.350281 seconds
2025-06-28 05:57:41 UTC+0000 -         ~   Running Charging analysis
2025-06-28 05:57:42 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.522939 seconds
2025-06-28 05:57:42 UTC+0000 -         ~   Pre-caching kpis
2025-06-28 05:58:03 UTC+0000 - Can't find a file matching pattern: Freight.sqlite
2025-06-28 05:58:05 UTC+0000 - No target and simulation data
2025-06-28 05:58:05 UTC+0000 - Saving precaching KPIs runtime report, total time: 23.4s
2025-06-28 05:58:05 UTC+0000 -         ~   Pre-caching kpis: Done in 0:00:23.445704 seconds
2025-06-28 05:58:05 UTC+0000 -         ~   Compressing files
2025-06-28 05:58:05 UTC+0000 -         ~     Compressing ['Bloomington-Result.sqlite', 'Bloomington-Demand.sqlite', 'Bloomington-Supply.sqlite'] in dir /tmp/model_build/Bloomington_iteration_3
2025-06-28 05:58:05 UTC+0000 -         ~       - Bloomington-Result.sqlite
2025-06-28 05:58:05 UTC+0000 -         ~       - Bloomington-Demand.sqlite
2025-06-28 05:58:05 UTC+0000 -         ~       - Bloomington-Supply.sqlite
2025-06-28 05:58:06 UTC+0000 -         ~   Checking that compressed files were created cleanly
2025-06-28 05:58:06 UTC+0000 -         ~   All tar.gz files pass sanity checking
2025-06-28 05:58:06 UTC+0000 -         ~   Deleting files which were compressed
2025-06-28 05:58:06 UTC+0000 -         ~   Compressing files: Done in 0:00:00.435163 seconds
2025-06-28 05:58:06 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_3: Done in 0:00:28.659381 seconds
2025-06-28 07:11:54 UTC+0000 -   Running Polaris: Done in 1:14:16.215305 seconds
2025-06-28 07:11:54 UTC+0000 - Model run results are in dir: /tmp/model_build/Bloomington_iteration_4
2025-06-28 07:11:54 UTC+0000 -   Default End of Loop Function
2025-06-28 07:11:54 UTC+0000 -     normal iteration - copying back highway skim file
2025-06-28 07:11:54 UTC+0000 -     normal iteration - copying back demand db
2025-06-28 07:11:54 UTC+0000 -     normal iteration - copying back result db
2025-06-28 07:11:54 UTC+0000 -     finished copying back files
2025-06-28 07:11:55 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 07:11:55 UTC+0000 - cmd=['/tmp/venv_model_run/lib/python3.12/site-packages/polaris/bin/Integrated_Model', '--version']
2025-06-28 07:11:55 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_4
2025-06-28 07:11:55 UTC+0000 - Waiting for async threads to complete
2025-06-28 07:11:55 UTC+0000 -         ~   Running WTF analysis
2025-06-28 07:11:59 UTC+0000 - Adding Spatialite infrastructure to the database
2025-06-28 07:11:59 UTC+0000 - Spatialite is working with version 5.1.0
2025-06-28 07:11:59 UTC+0000 -         ~   Running WTF analysis: Done in 0:00:03.623251 seconds
2025-06-28 07:11:59 UTC+0000 -         ~   Generating Gap Reports
2025-06-28 07:11:59 UTC+0000 -             - /tmp/model_build/Bloomington_iteration_4
2025-06-28 07:11:59 UTC+0000 -         ~   Generating Gap Reports: Done in 0:00:00.308547 seconds
2025-06-28 07:11:59 UTC+0000 -         ~   Running Charging analysis
2025-06-28 07:12:00 UTC+0000 -         ~   Running Charging analysis: Done in 0:00:00.525162 seconds
2025-06-28 07:12:00 UTC+0000 -         ~   Pre-caching kpis
2025-06-28 07:12:21 UTC+0000 - Can't find a file matching pattern: Freight.sqlite
2025-06-28 07:12:22 UTC+0000 - No target and simulation data
2025-06-28 07:12:22 UTC+0000 - Saving precaching KPIs runtime report, total time: 22.3s
2025-06-28 07:12:22 UTC+0000 -         ~   Pre-caching kpis: Done in 0:00:22.380421 seconds
2025-06-28 07:12:22 UTC+0000 -         ~ RUNNING in background thread for iteration iteration_4: Done in 0:00:26.903659 seconds
2025-06-28 07:12:22 UTC+0000 - FINISHED MAIN LOOP
2025-07-01 09:08:00 CDT-0500 - Using Polaris-Studio git sha: b'25bb9b497e689dcc8fad72aadaface3e110afe7d\n'
2025-07-01 09:08:00 CDT-0500 - Running Convergence Loop using the following config
2025-07-01 09:08:00 CDT-0500 -   uuid                                        = 3a79a5a91ac446c28f6fc07daa5af0b3
2025-07-01 09:08:00 CDT-0500 -   data_dir                                    = /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:08:00 CDT-0500 -   backup_dir                                  = None
2025-07-01 09:08:00 CDT-0500 -   archive_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/archive
2025-07-01 09:08:00 CDT-0500 -   results_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/simulation_results
2025-07-01 09:08:00 CDT-0500 -   db_name                                     = Bloomington
2025-07-01 09:08:00 CDT-0500 -   polaris_exe                                 = /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:00 CDT-0500 -   scenario_skim_file                          = scenario_abm.json
2025-07-01 09:08:00 CDT-0500 -   scenario_main_init                          = scenario_abm.json
2025-07-01 09:08:00 CDT-0500 -   scenario_main                               = scenario_abm.json
2025-07-01 09:08:00 CDT-0500 -   async_inline                                = False
2025-07-01 09:08:00 CDT-0500 -   num_threads                                 = 20
2025-07-01 09:08:00 CDT-0500 -   num_abm_runs                                = 4
2025-07-01 09:08:00 CDT-0500 -   num_dta_runs                                = 0
2025-07-01 09:08:00 CDT-0500 -   num_outer_loops                             = 1
2025-07-01 09:08:00 CDT-0500 -   start_iteration_from                        = None
2025-07-01 09:08:00 CDT-0500 -   num_retries                                 = 1
2025-07-01 09:08:00 CDT-0500 -   use_numa                                    = True
2025-07-01 09:08:00 CDT-0500 -   do_skim                                     = True
2025-07-01 09:08:00 CDT-0500 -   do_abm_init                                 = True
2025-07-01 09:08:00 CDT-0500 -   do_pop_synth                                = False
2025-07-01 09:08:00 CDT-0500 -   workplace_stabilization                     = {'enabled': False, 'schedule': {'first_iteration': 1, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}
2025-07-01 09:08:00 CDT-0500 -   calibration                                 = {'enabled': False, 'target_csv_dir': PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/calibration_targets'), 'calibration_schedule': {'destination': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'mode': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'activity': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'timing': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}, 'destination_vot_max_adj': 0.2, 'num_planned_activity_iterations': 0, 'step_size': 2.0}
2025-07-01 09:08:00 CDT-0500 -   freight                                     = {'enabled': False, 'b2b_demand_synthesis_schedule': {'first_iteration': 1, 'last_iteration': 0, 'every_x_iter': 1, 'pattern': None, 'on_abm_init': True}, 'model_deliveries_schedule': {'first_iteration': 5, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': True}}
2025-07-01 09:08:00 CDT-0500 -   do_routing_MSA                              = False
2025-07-01 09:08:00 CDT-0500 -   realtime_informed_vehicle_market_share      = None
2025-07-01 09:08:00 CDT-0500 -   skim_averaging_factor                       = None
2025-07-01 09:08:00 CDT-0500 -   capacity_expressway                         = None
2025-07-01 09:08:00 CDT-0500 -   capacity_arterial                           = None
2025-07-01 09:08:00 CDT-0500 -   capacity_local                              = None
2025-07-01 09:08:00 CDT-0500 -   population_scale_factor                     = 0.25
2025-07-01 09:08:00 CDT-0500 -   trajectory_sampling                         = 0.01
2025-07-01 09:08:00 CDT-0500 -   add_rsus                                    = False
2025-07-01 09:08:00 CDT-0500 -   rsu_highway_pr                              = 0.0
2025-07-01 09:08:00 CDT-0500 -   rsu_major_pr                                = 0.0
2025-07-01 09:08:00 CDT-0500 -   rsu_minor_pr                                = 0.0
2025-07-01 09:08:00 CDT-0500 -   rsu_local_pr                                = 0.0
2025-07-01 09:08:00 CDT-0500 -   rsu_enabled_switching                       = False
2025-07-01 09:08:00 CDT-0500 -   fixed_connectivity_penetration_rates_for_cv = None
2025-07-01 09:08:00 CDT-0500 -   highway_skim_file_name                      = highway_skim_file.omx
2025-07-01 09:08:00 CDT-0500 -   transit_skim_file_name                      = transit_skim_file.omx
2025-07-01 09:08:00 CDT-0500 -   skim_interval_endpoints                     = [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:08:00 CDT-0500 -   seed                                        = None
2025-07-01 09:08:00 CDT-0500 -   skim_seed                                   = None
2025-07-01 09:08:00 CDT-0500 -   skip_spatial_on_windows                     = False
2025-07-01 09:08:00 CDT-0500 -   user_data                                   = None
2025-07-01 09:08:00 CDT-0500 - POLARIS Executable:
2025-07-01 09:08:00 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', '--version']
2025-07-01 09:08:00 CDT-0500 -     path: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:00 CDT-0500 -   exists: ✔
2025-07-01 09:08:00 CDT-0500 -    built: 2025/06/16 02:11:03 UTC (15-11:56:58 ago)
2025-07-01 09:08:00 CDT-0500 -   branch: HEAD
2025-07-01 09:08:00 CDT-0500 -      SHA: e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:08:00 CDT-0500 -      url: https://git-out.gss.anl.gov/polaris/code/polaris-linux/-/commit/e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:08:00 CDT-0500 - Running the following iterations: 
2025-07-01 09:08:00 CDT-0500 -    ['00_skim_iteration', '01_abm_init_iteration', 'iteration_1', 'iteration_2', 'iteration_3', 'iteration_4']
2025-07-01 09:08:01 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:08:01 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:08:01 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:08:01 CDT-0500 - Setting backups...
2025-07-01 09:08:01 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:08:01 CDT-0500 - 
2025-07-01 09:08:01 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:08:01 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:08:01 CDT-0500 -   Running Polaris
2025-07-01 09:08:01 CDT-0500 -   The following modifications will be applied to /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.json:
2025-07-01 09:08:01 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:08:01 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:08:01 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:08:01 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:08:01 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:08:01 CDT-0500 -     replan                               : {}
2025-07-01 09:08:01 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:08:01 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:08:01 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:08:01 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:08:01 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:08:01 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:08:01 CDT-0500 -     EV_charging                          : False
2025-07-01 09:08:01 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:08:01 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:08:01 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:08:01 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:08:01 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:08:01 CDT-0500 -     output_directory                     : Bloomington_00_skim_iteration
2025-07-01 09:08:01 CDT-0500 -     database_name                        : Bloomington
2025-07-01 09:08:01 CDT-0500 -     input_result_database_name           : Bloomington
2025-07-01 09:08:01 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:01 CDT-0500 -       arg1: /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json
2025-07-01 09:08:01 CDT-0500 -       arg2: 20
2025-07-01 09:08:01 CDT-0500 -        dir: /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:08:01 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json'), '20']
2025-07-01 09:08:01 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json 20
2025-07-01 09:08:01 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:08:01 CDT-0500 - /home/<USER>/POLARIS/Bloomington-********/Model/Bloomington_00_skim_iteration was not created, not retrying
2025-07-01 09:08:55 CDT-0500 - Using Polaris-Studio git sha: b'25bb9b497e689dcc8fad72aadaface3e110afe7d\n'
2025-07-01 09:08:55 CDT-0500 - Running Convergence Loop using the following config
2025-07-01 09:08:55 CDT-0500 -   uuid                                        = 5f5bd68fda3c4c7e82b1e6159ae6591c
2025-07-01 09:08:55 CDT-0500 -   data_dir                                    = /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:08:55 CDT-0500 -   backup_dir                                  = None
2025-07-01 09:08:55 CDT-0500 -   archive_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/archive
2025-07-01 09:08:55 CDT-0500 -   results_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/simulation_results
2025-07-01 09:08:55 CDT-0500 -   db_name                                     = Bloomington
2025-07-01 09:08:55 CDT-0500 -   polaris_exe                                 = /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:55 CDT-0500 -   scenario_skim_file                          = scenario_abm.json
2025-07-01 09:08:55 CDT-0500 -   scenario_main_init                          = scenario_abm.json
2025-07-01 09:08:55 CDT-0500 -   scenario_main                               = scenario_abm.json
2025-07-01 09:08:55 CDT-0500 -   async_inline                                = False
2025-07-01 09:08:55 CDT-0500 -   num_threads                                 = 4
2025-07-01 09:08:55 CDT-0500 -   num_abm_runs                                = 4
2025-07-01 09:08:55 CDT-0500 -   num_dta_runs                                = 0
2025-07-01 09:08:55 CDT-0500 -   num_outer_loops                             = 1
2025-07-01 09:08:55 CDT-0500 -   start_iteration_from                        = None
2025-07-01 09:08:55 CDT-0500 -   num_retries                                 = 1
2025-07-01 09:08:55 CDT-0500 -   use_numa                                    = True
2025-07-01 09:08:55 CDT-0500 -   do_skim                                     = True
2025-07-01 09:08:55 CDT-0500 -   do_abm_init                                 = True
2025-07-01 09:08:55 CDT-0500 -   do_pop_synth                                = False
2025-07-01 09:08:55 CDT-0500 -   workplace_stabilization                     = {'enabled': False, 'schedule': {'first_iteration': 1, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}
2025-07-01 09:08:55 CDT-0500 -   calibration                                 = {'enabled': False, 'target_csv_dir': PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/calibration_targets'), 'calibration_schedule': {'destination': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'mode': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'activity': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'timing': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}, 'destination_vot_max_adj': 0.2, 'num_planned_activity_iterations': 0, 'step_size': 2.0}
2025-07-01 09:08:55 CDT-0500 -   freight                                     = {'enabled': False, 'b2b_demand_synthesis_schedule': {'first_iteration': 1, 'last_iteration': 0, 'every_x_iter': 1, 'pattern': None, 'on_abm_init': True}, 'model_deliveries_schedule': {'first_iteration': 5, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': True}}
2025-07-01 09:08:55 CDT-0500 -   do_routing_MSA                              = False
2025-07-01 09:08:55 CDT-0500 -   realtime_informed_vehicle_market_share      = None
2025-07-01 09:08:55 CDT-0500 -   skim_averaging_factor                       = None
2025-07-01 09:08:55 CDT-0500 -   capacity_expressway                         = None
2025-07-01 09:08:55 CDT-0500 -   capacity_arterial                           = None
2025-07-01 09:08:55 CDT-0500 -   capacity_local                              = None
2025-07-01 09:08:55 CDT-0500 -   population_scale_factor                     = 0.25
2025-07-01 09:08:55 CDT-0500 -   trajectory_sampling                         = 0.01
2025-07-01 09:08:55 CDT-0500 -   add_rsus                                    = False
2025-07-01 09:08:55 CDT-0500 -   rsu_highway_pr                              = 0.0
2025-07-01 09:08:55 CDT-0500 -   rsu_major_pr                                = 0.0
2025-07-01 09:08:55 CDT-0500 -   rsu_minor_pr                                = 0.0
2025-07-01 09:08:55 CDT-0500 -   rsu_local_pr                                = 0.0
2025-07-01 09:08:55 CDT-0500 -   rsu_enabled_switching                       = False
2025-07-01 09:08:55 CDT-0500 -   fixed_connectivity_penetration_rates_for_cv = None
2025-07-01 09:08:55 CDT-0500 -   highway_skim_file_name                      = highway_skim_file.omx
2025-07-01 09:08:55 CDT-0500 -   transit_skim_file_name                      = transit_skim_file.omx
2025-07-01 09:08:55 CDT-0500 -   skim_interval_endpoints                     = [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:08:55 CDT-0500 -   seed                                        = None
2025-07-01 09:08:55 CDT-0500 -   skim_seed                                   = None
2025-07-01 09:08:55 CDT-0500 -   skip_spatial_on_windows                     = False
2025-07-01 09:08:55 CDT-0500 -   user_data                                   = None
2025-07-01 09:08:55 CDT-0500 - POLARIS Executable:
2025-07-01 09:08:55 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', '--version']
2025-07-01 09:08:55 CDT-0500 -     path: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:55 CDT-0500 -   exists: ✔
2025-07-01 09:08:55 CDT-0500 -    built: 2025/06/16 02:11:03 UTC (15-11:57:52 ago)
2025-07-01 09:08:55 CDT-0500 -   branch: HEAD
2025-07-01 09:08:55 CDT-0500 -      SHA: e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:08:55 CDT-0500 -      url: https://git-out.gss.anl.gov/polaris/code/polaris-linux/-/commit/e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:08:55 CDT-0500 - Running the following iterations: 
2025-07-01 09:08:55 CDT-0500 -    ['00_skim_iteration', '01_abm_init_iteration', 'iteration_1', 'iteration_2', 'iteration_3', 'iteration_4']
2025-07-01 09:08:55 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:08:55 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:08:55 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:08:55 CDT-0500 - Setting backups...
2025-07-01 09:08:55 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:08:55 CDT-0500 - 
2025-07-01 09:08:55 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:08:55 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:08:55 CDT-0500 -   Running Polaris
2025-07-01 09:08:55 CDT-0500 -   The following modifications will be applied to /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.json:
2025-07-01 09:08:55 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:08:55 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:08:55 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:08:55 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:08:55 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:08:55 CDT-0500 -     replan                               : {}
2025-07-01 09:08:55 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:08:55 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:08:55 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:08:55 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:08:55 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:08:55 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:08:55 CDT-0500 -     EV_charging                          : False
2025-07-01 09:08:55 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:08:55 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:08:55 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:08:55 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:08:55 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:08:55 CDT-0500 -     output_directory                     : Bloomington_00_skim_iteration
2025-07-01 09:08:55 CDT-0500 -     database_name                        : Bloomington
2025-07-01 09:08:55 CDT-0500 -     input_result_database_name           : Bloomington
2025-07-01 09:08:55 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:08:55 CDT-0500 -       arg1: /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json
2025-07-01 09:08:55 CDT-0500 -       arg2: 4
2025-07-01 09:08:55 CDT-0500 -        dir: /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:08:55 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json'), '4']
2025-07-01 09:08:55 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json 4
2025-07-01 09:08:55 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:08:55 CDT-0500 - /home/<USER>/POLARIS/Bloomington-********/Model/Bloomington_00_skim_iteration was not created, not retrying
2025-07-01 09:09:31 CDT-0500 - Using Polaris-Studio git sha: b'25bb9b497e689dcc8fad72aadaface3e110afe7d\n'
2025-07-01 09:09:31 CDT-0500 - Running Convergence Loop using the following config
2025-07-01 09:09:31 CDT-0500 -   uuid                                        = 6c02b50cddc94ad891b8f7e6d4f50b94
2025-07-01 09:09:31 CDT-0500 -   data_dir                                    = /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:09:31 CDT-0500 -   backup_dir                                  = None
2025-07-01 09:09:31 CDT-0500 -   archive_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/archive
2025-07-01 09:09:31 CDT-0500 -   results_dir                                 = /home/<USER>/POLARIS/Bloomington-********/Model/simulation_results
2025-07-01 09:09:31 CDT-0500 -   db_name                                     = Bloomington
2025-07-01 09:09:31 CDT-0500 -   polaris_exe                                 = /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:09:31 CDT-0500 -   scenario_skim_file                          = scenario_abm.json
2025-07-01 09:09:31 CDT-0500 -   scenario_main_init                          = scenario_abm.json
2025-07-01 09:09:31 CDT-0500 -   scenario_main                               = scenario_abm.json
2025-07-01 09:09:31 CDT-0500 -   async_inline                                = False
2025-07-01 09:09:31 CDT-0500 -   num_threads                                 = 4
2025-07-01 09:09:31 CDT-0500 -   num_abm_runs                                = 4
2025-07-01 09:09:31 CDT-0500 -   num_dta_runs                                = 0
2025-07-01 09:09:31 CDT-0500 -   num_outer_loops                             = 1
2025-07-01 09:09:31 CDT-0500 -   start_iteration_from                        = None
2025-07-01 09:09:31 CDT-0500 -   num_retries                                 = 1
2025-07-01 09:09:31 CDT-0500 -   use_numa                                    = True
2025-07-01 09:09:31 CDT-0500 -   do_skim                                     = True
2025-07-01 09:09:31 CDT-0500 -   do_abm_init                                 = True
2025-07-01 09:09:31 CDT-0500 -   do_pop_synth                                = False
2025-07-01 09:09:31 CDT-0500 -   workplace_stabilization                     = {'enabled': False, 'schedule': {'first_iteration': 1, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}
2025-07-01 09:09:31 CDT-0500 -   calibration                                 = {'enabled': False, 'target_csv_dir': PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/calibration_targets'), 'calibration_schedule': {'destination': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'mode': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'activity': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}, 'timing': {'first_iteration': 1, 'last_iteration': 21, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': False}}, 'destination_vot_max_adj': 0.2, 'num_planned_activity_iterations': 0, 'step_size': 2.0}
2025-07-01 09:09:31 CDT-0500 -   freight                                     = {'enabled': False, 'b2b_demand_synthesis_schedule': {'first_iteration': 1, 'last_iteration': 0, 'every_x_iter': 1, 'pattern': None, 'on_abm_init': True}, 'model_deliveries_schedule': {'first_iteration': 5, 'last_iteration': 31, 'every_x_iter': 5, 'pattern': None, 'on_abm_init': True}}
2025-07-01 09:09:31 CDT-0500 -   do_routing_MSA                              = False
2025-07-01 09:09:31 CDT-0500 -   realtime_informed_vehicle_market_share      = None
2025-07-01 09:09:31 CDT-0500 -   skim_averaging_factor                       = None
2025-07-01 09:09:31 CDT-0500 -   capacity_expressway                         = None
2025-07-01 09:09:31 CDT-0500 -   capacity_arterial                           = None
2025-07-01 09:09:31 CDT-0500 -   capacity_local                              = None
2025-07-01 09:09:31 CDT-0500 -   population_scale_factor                     = 0.25
2025-07-01 09:09:31 CDT-0500 -   trajectory_sampling                         = 0.01
2025-07-01 09:09:31 CDT-0500 -   add_rsus                                    = False
2025-07-01 09:09:31 CDT-0500 -   rsu_highway_pr                              = 0.0
2025-07-01 09:09:31 CDT-0500 -   rsu_major_pr                                = 0.0
2025-07-01 09:09:31 CDT-0500 -   rsu_minor_pr                                = 0.0
2025-07-01 09:09:31 CDT-0500 -   rsu_local_pr                                = 0.0
2025-07-01 09:09:31 CDT-0500 -   rsu_enabled_switching                       = False
2025-07-01 09:09:31 CDT-0500 -   fixed_connectivity_penetration_rates_for_cv = None
2025-07-01 09:09:31 CDT-0500 -   highway_skim_file_name                      = highway_skim_file.omx
2025-07-01 09:09:31 CDT-0500 -   transit_skim_file_name                      = transit_skim_file.omx
2025-07-01 09:09:31 CDT-0500 -   skim_interval_endpoints                     = [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:09:31 CDT-0500 -   seed                                        = None
2025-07-01 09:09:31 CDT-0500 -   skim_seed                                   = None
2025-07-01 09:09:31 CDT-0500 -   skip_spatial_on_windows                     = False
2025-07-01 09:09:31 CDT-0500 -   user_data                                   = None
2025-07-01 09:09:31 CDT-0500 - POLARIS Executable:
2025-07-01 09:09:31 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', '--version']
2025-07-01 09:09:31 CDT-0500 -     path: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:09:31 CDT-0500 -   exists: ✔
2025-07-01 09:09:31 CDT-0500 -    built: 2025/06/16 02:11:03 UTC (15-11:58:28 ago)
2025-07-01 09:09:31 CDT-0500 -   branch: HEAD
2025-07-01 09:09:31 CDT-0500 -      SHA: e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:09:31 CDT-0500 -      url: https://git-out.gss.anl.gov/polaris/code/polaris-linux/-/commit/e6696b54cd820ddefe25375f0cd0aa8e82399e01
2025-07-01 09:09:31 CDT-0500 - Running the following iterations: 
2025-07-01 09:09:31 CDT-0500 -    ['00_skim_iteration', '01_abm_init_iteration', 'iteration_1', 'iteration_2', 'iteration_3', 'iteration_4']
2025-07-01 09:09:31 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:09:31 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:09:31 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:09:31 CDT-0500 - Setting backups...
2025-07-01 09:09:31 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:09:31 CDT-0500 - 
2025-07-01 09:09:31 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:09:31 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:09:31 CDT-0500 -   Running Polaris
2025-07-01 09:09:31 CDT-0500 -   The following modifications will be applied to /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.json:
2025-07-01 09:09:31 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:09:31 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:09:31 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:09:31 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:09:31 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:09:31 CDT-0500 -     replan                               : {}
2025-07-01 09:09:31 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:09:31 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:09:31 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:09:31 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:09:31 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:09:31 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:09:31 CDT-0500 -     EV_charging                          : False
2025-07-01 09:09:31 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:09:31 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:09:31 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:09:31 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:09:31 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:09:31 CDT-0500 -     output_directory                     : Bloomington_00_skim_iteration
2025-07-01 09:09:31 CDT-0500 -     database_name                        : Bloomington
2025-07-01 09:09:31 CDT-0500 -     input_result_database_name           : Bloomington
2025-07-01 09:09:31 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:09:31 CDT-0500 -       arg1: /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json
2025-07-01 09:09:31 CDT-0500 -       arg2: 4
2025-07-01 09:09:31 CDT-0500 -        dir: /home/<USER>/POLARIS/Bloomington-********/Model
2025-07-01 09:09:31 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('/home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json'), '4']
2025-07-01 09:09:31 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model /home/<USER>/POLARIS/Bloomington-********/Model/scenario_abm.modified.json 4
2025-07-01 09:09:31 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:09:31 CDT-0500 - /home/<USER>/POLARIS/Bloomington-********/Model/Bloomington_00_skim_iteration was not created, not retrying
2025-07-01 09:52:12 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:52:12 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:52:12 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:52:12 CDT-0500 - Setting backups...
2025-07-01 09:52:12 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:52:12 CDT-0500 - 
2025-07-01 09:52:12 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:52:12 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:52:12 CDT-0500 -   Running Polaris
2025-07-01 09:52:12 CDT-0500 -   The following modifications will be applied to scenario_abm.json:
2025-07-01 09:52:12 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:52:12 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:52:12 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:52:12 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:52:12 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:52:12 CDT-0500 -     replan                               : {}
2025-07-01 09:52:12 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:52:12 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:52:12 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:52:12 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:52:12 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:52:12 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:52:12 CDT-0500 -     EV_charging                          : False
2025-07-01 09:52:12 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:52:12 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:52:12 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:52:12 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:52:12 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:52:12 CDT-0500 -     output_directory                     : None_00_skim_iteration
2025-07-01 09:52:12 CDT-0500 -     database_name                        : None
2025-07-01 09:52:12 CDT-0500 -     input_result_database_name           : None
2025-07-01 09:52:12 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:52:12 CDT-0500 -       arg1: scenario_abm.modified.json
2025-07-01 09:52:12 CDT-0500 -       arg2: 20
2025-07-01 09:52:12 CDT-0500 -        dir: .
2025-07-01 09:52:12 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('scenario_abm.modified.json'), '20']
2025-07-01 09:52:12 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model scenario_abm.modified.json 20
2025-07-01 09:52:12 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:52:12 CDT-0500 - None_00_skim_iteration was not created, not retrying
2025-07-01 09:53:10 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:53:10 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:53:10 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:53:10 CDT-0500 - Setting backups...
2025-07-01 09:53:10 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:53:10 CDT-0500 - 
2025-07-01 09:53:10 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:53:10 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:53:10 CDT-0500 -   Running Polaris
2025-07-01 09:53:10 CDT-0500 -   The following modifications will be applied to scenario_abm.json:
2025-07-01 09:53:10 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:53:10 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:53:10 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:53:10 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:53:10 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:53:10 CDT-0500 -     replan                               : {}
2025-07-01 09:53:10 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:53:10 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:53:10 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:53:10 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:53:10 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:53:10 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:53:10 CDT-0500 -     EV_charging                          : False
2025-07-01 09:53:10 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:53:10 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:53:10 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:53:10 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:53:10 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:53:10 CDT-0500 -     output_directory                     : None_00_skim_iteration
2025-07-01 09:53:10 CDT-0500 -     database_name                        : None
2025-07-01 09:53:10 CDT-0500 -     input_result_database_name           : None
2025-07-01 09:53:10 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:53:10 CDT-0500 -       arg1: scenario_abm.modified.json
2025-07-01 09:53:10 CDT-0500 -       arg2: 20
2025-07-01 09:53:10 CDT-0500 -        dir: .
2025-07-01 09:53:10 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('scenario_abm.modified.json'), '20']
2025-07-01 09:53:10 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model scenario_abm.modified.json 20
2025-07-01 09:53:10 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:53:10 CDT-0500 - None_00_skim_iteration was not created, not retrying
2025-07-01 09:53:44 CDT-0500 - Missing table activity_type, skipping FK check
2025-07-01 09:53:44 CDT-0500 - Jam density not set in the configuration. Using default value of 220 veh/mile
2025-07-01 09:53:44 CDT-0500 - The Supply file has no additional consistency issues
2025-07-01 09:53:44 CDT-0500 - Setting backups...
2025-07-01 09:53:44 CDT-0500 - Running transit summary sql file: /home/<USER>/.local/lib/python3.10/site-packages/polaris/runs/sql/transit_stats.sql
2025-07-01 09:53:44 CDT-0500 - 
2025-07-01 09:53:44 CDT-0500 - Starting iteration: 00_skim_iteration
2025-07-01 09:53:44 CDT-0500 -   Cleaning database for ABM
2025-07-01 09:53:44 CDT-0500 -   Running Polaris
2025-07-01 09:53:44 CDT-0500 -   The following modifications will be applied to scenario_abm.json:
2025-07-01 09:53:44 CDT-0500 -     time_dependent_routing               : False
2025-07-01 09:53:44 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:53:44 CDT-0500 -     percent_to_synthesize                : 0.0
2025-07-01 09:53:44 CDT-0500 -     read_trip_factors                    : {'External': 0.0}
2025-07-01 09:53:44 CDT-0500 -     read_population_from_database        : False
2025-07-01 09:53:44 CDT-0500 -     replan                               : {}
2025-07-01 09:53:44 CDT-0500 -     vehicle_trajectory_sample_rate       : 0.0
2025-07-01 09:53:44 CDT-0500 -     skim_nodes_per_zone                  : 4
2025-07-01 09:53:44 CDT-0500 -     read_skim_tables                     : False
2025-07-01 09:53:44 CDT-0500 -     write_skim_tables                    : True
2025-07-01 09:53:44 CDT-0500 -     generate_transit_skims               : True
2025-07-01 09:53:44 CDT-0500 -     skim_interval_endpoint_minutes       : [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440]
2025-07-01 09:53:44 CDT-0500 -     EV_charging                          : False
2025-07-01 09:53:44 CDT-0500 -     use_tnc_system                       : True
2025-07-01 09:53:44 CDT-0500 -     tnc_feedback                         : False
2025-07-01 09:53:44 CDT-0500 -     time_dependent_routing_weight_factor : 1.0
2025-07-01 09:53:44 CDT-0500 -     use_freight_model                    : False
2025-07-01 09:53:44 CDT-0500 -     traffic_scale_factor                 : 0.25
2025-07-01 09:53:44 CDT-0500 -     output_directory                     : Bloomington_00_skim_iteration
2025-07-01 09:53:44 CDT-0500 -     database_name                        : Bloomington
2025-07-01 09:53:44 CDT-0500 -     input_result_database_name           : Bloomington
2025-07-01 09:53:44 CDT-0500 -        exe: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model
2025-07-01 09:53:44 CDT-0500 -       arg1: scenario_abm.modified.json
2025-07-01 09:53:44 CDT-0500 -       arg2: 20
2025-07-01 09:53:44 CDT-0500 -        dir: .
2025-07-01 09:53:44 CDT-0500 - cmd=['/home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model', PosixPath('scenario_abm.modified.json'), '20']
2025-07-01 09:53:45 CDT-0500 - Non-zero exit code (-6) returned for cmd: /home/<USER>/.local/lib/python3.10/site-packages/polaris/bin/Integrated_Model scenario_abm.modified.json 20
2025-07-01 09:53:45 CDT-0500 - Got an exception: args=(RuntimeError('Command failed with exit_code -6'),)
2025-07-01 09:53:45 CDT-0500 - Bloomington_00_skim_iteration was not created, not retrying

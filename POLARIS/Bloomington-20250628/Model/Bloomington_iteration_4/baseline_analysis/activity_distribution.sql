DROP TABLE IF EXISTS person_distribution;
CREATE TABLE IF NOT EXISTS person_distribution AS
SELECT CASE WHEN (p.employment = 4 OR p.employment = 1) AND work_hours >= 30     THEN 'FULLTIME_WORKER'
                WHEN (p.employment = 4 OR p.employment = 1)                          THEN 'PARTTIME_WORKER'
                WHEN (school_enrollment = 3 OR school_enrollment = 2) AND p.age > 18 THEN 'ADULT_STUDENT'
                WHEN p.age >= 65                                                     THEN 'SENIOR'
                WHEN p.age < 65 AND p.age > 18                                       THEN 'NONWORKER'
                WHEN p.age >= 16 AND p.age <= 18                                     THEN 'STUDENT_DRIVER'
                WHEN p.age < 16 AND p.age >= 5                                       THEN 'SCHOOL_CHILD'
                WHEN p.age < 5                                                       THEN 'PRESCHOOL'
                ELSE                                                                      'NONWORKER'
           END AS pertype, CAST(COUNT(*) as real) AS total
FROM person p
GROUP BY pertype;

DROP TABLE IF EXISTS activity_distribution;
CREATE TABLE IF NOT EXISTS activity_distribution AS
SELECT CASE WHEN (p.employment = 4 OR p.employment = 1) AND work_hours >= 30     THEN 'FULLTIME_WORKER'
                WHEN (p.employment = 4 OR p.employment = 1)                          THEN 'PARTTIME_WORKER'
                WHEN (school_enrollment = 3 OR school_enrollment = 2) AND p.age > 18 THEN 'ADULT_STUDENT'
                WHEN p.age >= 65                                                     THEN 'SENIOR'
                WHEN p.age < 65 AND p.age > 18                                       THEN 'NONWORKER'
                WHEN p.age >= 16 AND p.age <= 18                                     THEN 'STUDENT_DRIVER'
                WHEN p.age < 16 AND p.age >= 5                                       THEN 'SCHOOL_CHILD'
                WHEN p.age < 5                                                       THEN 'PRESCHOOL'
                ELSE                                                                      'NONWORKER'
           END AS pertype,
       CASE WHEN a.type='EAT OUT'         THEN 'EAT_OUT'
                WHEN a.type='ERRANDS'         THEN 'ERRANDS'
                WHEN a.type='HEALTHCARE'      THEN 'HEALTHCARE'
                WHEN a.type='HOME'            THEN 'HOME'
                WHEN a.type='LEISURE'         THEN 'LEISURE'
                WHEN a.type='PERSONAL'        THEN 'PERSONAL_BUSINESS'
                WHEN a.type='PICKUP-DROPOFF'  THEN 'PICKUP_DROPOFF'
                WHEN a.type='RELIGIOUS-CIVIC' THEN 'RELIGIOUS_OR_CIVIC'
                WHEN a.type='SCHOOL'          THEN 'SCHOOL'
                WHEN a.type='SERVICE'         THEN 'SERVICE_VEHICLE'
                WHEN a.type='SHOP-MAJOR'      THEN 'MAJOR_SHOPPING'
                WHEN a.type='SOCIAL'          THEN 'SOCIAL'
                WHEN a.type='WORK'            THEN 'WORK'
                WHEN a.type='WORK AT HOME'    THEN 'WORK'
                WHEN a.type='PART_WORK'       THEN 'PART_TIME_WORK'
                WHEN a.type='SHOP-OTHER'      THEN 'OTHER_SHOPPING'
                WHEN a.type='EV_CHARGING'     THEN 'EV_CHARGING'
                ELSE                               'MISSING:' || type
                END as acttype,
       (CASE WHEN a.trip == 0 THEN 'planned' ELSE 'executed' END) as activity_stage,
       cast(count(*) as real) as count
FROM person p, activity a
WHERE p.person = a.person
AND not (a.mode == 'NO_MOVE' AND a.type in ('HOME','WORK AT HOME','PICKUP-DROPOFF'))
AND (a.trip == 0 OR (a.Start_Time > 122 AND a.trip <> 0))
GROUP BY 1,2,3;

DROP TABLE IF EXISTS activity_rate_distribution;
CREATE TABLE activity_rate_distribution AS
SELECT ad.pertype AS pertype, ad.acttype AS acttype, ad.activity_stage, ad.count/pd.total AS rate, 
       pd.total as person_count, ad.count as activity_count
FROM activity_distribution ad, person_distribution pd
WHERE ad.pertype = pd.pertype
ORDER BY pertype, acttype;

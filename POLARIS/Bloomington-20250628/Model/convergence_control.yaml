do_abm_init: true
do_skim: true
num_abm_runs: 4
population_scale_factor: 0.25
num_threads: 20
# polaris_exe: ./bin/polaris-linux/Integrated_Model
# population_scale_factor: 0.25
# realtime_informed_vehicle_market_share: 0.0
# scenario_main: scenario_abm.json
# scenario_main_init: scenario_abm.json
# scenario_skim_file: scenario_abm.json
# time_dependent_routing_link_based_gap: false
# time_dependent_routing_weight_scale: 900.0
# trajectory_sampling: 0.01
# transit_optimisation: false
# vot_level: 2

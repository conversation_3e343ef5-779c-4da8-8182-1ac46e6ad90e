filename,source,param_name,value
"/tmp/model_build/scenario_abm.modified.json",default,"EV_charging","0"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_consumption_only","0"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_default_Wh_per_mile","370 whpm"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_fixed_consumption","0"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_home_charging_strategy","0"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_station_selection_strategy","5"
"/tmp/model_build/scenario_abm.modified.json",default,"EV_write_all_trajectories","0"
"/tmp/model_build/scenario_abm.modified.json",default,"GENERATE_EVCS","0"
"/tmp/model_build/scenario_abm.modified.json",default,"L3_automation_cost","340282346638528859811704183484516925440.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"L5_automation_cost","340282346638528859811704183484516925440.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"ML_model_filename",""
"/tmp/model_build/scenario_abm.modified.json",default,"Private_Charger_Access_For_PVs","0"
"/tmp/model_build/scenario_abm.modified.json",default,"Public_Charger_Access_For_Fleets","0"
"/tmp/model_build/scenario_abm.modified.json",default,"accident_event_duration_reduction","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"activity_start_time_model_file_name","start_time_duration_data.txt"
"/tmp/model_build/scenario_abm.modified.json",default,"analyze_link_groups_file_path_name","analyze_link_groups"
"/tmp/model_build/scenario_abm.modified.json",default,"api_strict_cv","0"
"/tmp/model_build/scenario_abm.modified.json",default,"batch_router_use_link_for_od","0"
"/tmp/model_build/scenario_abm.modified.json",default,"build_link_to_link_trees","0"
"/tmp/model_build/scenario_abm.modified.json",default,"buildings_geometry_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"cacc_capacity_adjustment_alpha","1.012100"
"/tmp/model_build/scenario_abm.modified.json",default,"cacc_capacity_adjustment_alpha_arterial","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"cacc_capacity_adjustment_alpha_expressway","1.012100"
"/tmp/model_build/scenario_abm.modified.json",default,"cacc_capacity_adjustment_beta","2.469700"
"/tmp/model_build/scenario_abm.modified.json",default,"calculate_realtime_moe","1"
"/tmp/model_build/scenario_abm.modified.json",default,"capacity_adjustment_arterial","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"capacity_adjustment_highway","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"cav_market_penetration","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"cav_vott_adjustment","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"color_cars_randomly","1"
"/tmp/model_build/scenario_abm.modified.json",default,"compare_with_historic_moe","0"
"/tmp/model_build/scenario_abm.modified.json",default,"congestion_pricing","0"
"/tmp/model_build/scenario_abm.modified.json",default,"count_integrated_in_network_vehicles_only","0"
"/tmp/model_build/scenario_abm.modified.json",default,"curb_hold_for_curb_time","0"
"/tmp/model_build/scenario_abm.modified.json",default,"curb_lanes_blocked","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"curb_time_freight_seconds","60.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"curb_time_sov_seconds","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"curb_time_tnc_seconds","30.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"deflate_level","4"
"/tmp/model_build/scenario_abm.modified.json",default,"delivery_choice_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"do_cav_vott_adjustment","0"
"/tmp/model_build/scenario_abm.modified.json",default,"do_planner_routing","0"
"/tmp/model_build/scenario_abm.modified.json",default,"early_exit","none"
"/tmp/model_build/scenario_abm.modified.json",default,"enroute_excessive_delay_factor","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"evcs_generator_file_name",""
"/tmp/model_build/scenario_abm.modified.json",default,"external_popsyn_solver_filename","PopSyn.dll"
"/tmp/model_build/scenario_abm.modified.json",default,"fmlm_min_attempts_cutoff","5"
"/tmp/model_build/scenario_abm.modified.json",default,"fmlm_success_prop_cutoff","0.250000"
"/tmp/model_build/scenario_abm.modified.json",default,"force_no_street_parking_in_all_links","0"
"/tmp/model_build/scenario_abm.modified.json",default,"fuel_efficiency","25.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"fuel_price","3.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"historic_demand_moe_directory",""
"/tmp/model_build/scenario_abm.modified.json",default,"historic_link_moe_file_path_name","historic_moe_link.csv"
"/tmp/model_build/scenario_abm.modified.json",default,"historic_network_moe_file_path_name","historic_realtime_moe_network.csv"
"/tmp/model_build/scenario_abm.modified.json",default,"input_demand_database_name","Bloomington-Demand.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"input_directory","/tmp/model_build"
"/tmp/model_build/scenario_abm.modified.json",default,"input_freight_database_name","Bloomington-Freight.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"input_network_snapshots_file_path_name","input_network_snapshots"
"/tmp/model_build/scenario_abm.modified.json",default,"input_supply_database_name","Bloomington-Supply.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"intrahousehold_vehicle_sharing_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"l3_type_availability_list","[]"
"/tmp/model_build/scenario_abm.modified.json",default,"l5_type_availability_list","[]"
"/tmp/model_build/scenario_abm.modified.json",default,"lc_bounds_in_acceleration","0"
"/tmp/model_build/scenario_abm.modified.json",default,"load_analyze_link_groups_from_file","0"
"/tmp/model_build/scenario_abm.modified.json",default,"log_traveler_utility","0"
"/tmp/model_build/scenario_abm.modified.json",default,"long_distance_freight_stopping_penalty","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"long_distance_passenger_stopping_penalty","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"long_distance_trip_threshold_in_miles","50.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"look_ahead_switching","1"
"/tmp/model_build/scenario_abm.modified.json",default,"managed_lanes","0"
"/tmp/model_build/scenario_abm.modified.json",default,"miliseconds_per_iteration","1000"
"/tmp/model_build/scenario_abm.modified.json",default,"min_return_home_activity_duration","1800.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"minimum_link_length_meters","80.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"minimum_pocket_length_lc","40.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"minimum_spacing_origin_loading","20.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"model_directory","/tmp/model_build"
"/tmp/model_build/scenario_abm.modified.json",default,"multimodal_network_input","0"
"/tmp/model_build/scenario_abm.modified.json",default,"multimodal_trip_interruption_scenario","0"
"/tmp/model_build/scenario_abm.modified.json",default,"non_car_propensity","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"normal_day_link_moe_file_path_name","normal_day_moe_link.csv"
"/tmp/model_build/scenario_abm.modified.json",default,"num_assignment_intervals_per_aggregation_interval","50"
"/tmp/model_build/scenario_abm.modified.json",default,"num_simulation_intervals_per_assignment_interval_cv","10"
"/tmp/model_build/scenario_abm.modified.json",default,"output_analzye_link_group_moe_for_assignment_interval","0"
"/tmp/model_build/scenario_abm.modified.json",default,"output_demand_database_name","Bloomington-Demand.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"output_freight_database_name","Bloomington-Freight.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"output_network_moe_for_assignment_interval","0"
"/tmp/model_build/scenario_abm.modified.json",default,"output_result_database_name","Bloomington-Result.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"output_supply_database_name","Bloomington-Supply.sqlite"
"/tmp/model_build/scenario_abm.modified.json",default,"pocket_size_arterial","12"
"/tmp/model_build/scenario_abm.modified.json",default,"pocket_size_expressway","20"
"/tmp/model_build/scenario_abm.modified.json",default,"pocket_size_local","8"
"/tmp/model_build/scenario_abm.modified.json",default,"pooling_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"ramp_metering_flag","0"
"/tmp/model_build/scenario_abm.modified.json",default,"read_network_snapshots","0"
"/tmp/model_build/scenario_abm.modified.json",default,"read_normal_day_link_moe","0"
"/tmp/model_build/scenario_abm.modified.json",default,"read_population_from_urbansim","0"
"/tmp/model_build/scenario_abm.modified.json",default,"relative_indifference_band_route_choice_mean","0.100000"
"/tmp/model_build/scenario_abm.modified.json",default,"rideshare_vott_adjustment","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"routing_with_snapshots","0"
"/tmp/model_build/scenario_abm.modified.json",default,"run_simulation_for_db_input","1"
"/tmp/model_build/scenario_abm.modified.json",default,"scale_traffic_flow_to_demand","1"
"/tmp/model_build/scenario_abm.modified.json",default,"simulate_cacc","0"
"/tmp/model_build/scenario_abm.modified.json",default,"skim_interval_length_minutes","1440"
"/tmp/model_build/scenario_abm.modified.json",default,"skim_minimum_sov_speed","5.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"skim_seed","0"
"/tmp/model_build/scenario_abm.modified.json",default,"speed_delta_logging","5.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"threshold_share_accessibility","0.010000"
"/tmp/model_build/scenario_abm.modified.json",default,"tile_imagery_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_allow_experienced_tt","1"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_gap_calculation_strategy","use_max"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_gap_cap","3.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_gap_cap_cv","3.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_link_based_gap","1"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_factor_affects_calculation","1"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_factor_affects_choice","1"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_factor_cv","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_location","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_location_cv","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_scale_cv","900.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_dependent_routing_weight_shape_cv","2.500000"
"/tmp/model_build/scenario_abm.modified.json",default,"time_of_day_period_endpoint_hours","[6 hr, 9 hr, 15 hr, 19 hr, 24 hr]"
"/tmp/model_build/scenario_abm.modified.json",default,"tnc_driver_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"tnc_feedback","0"
"/tmp/model_build/scenario_abm.modified.json",default,"tnc_operator_chooser_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"tnc_surge_change_duration_minutes","15.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"tncandride_base_fare","0.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"tncandride_cost_per_mile","2.250000"
"/tmp/model_build/scenario_abm.modified.json",default,"tncandride_cost_per_minute","0.330000"
"/tmp/model_build/scenario_abm.modified.json",default,"tncandride_vott_adjustment","1.000000"
"/tmp/model_build/scenario_abm.modified.json",default,"traffic_midblock_start_end","0"
"/tmp/model_build/scenario_abm.modified.json",default,"turn_on_red","1"
"/tmp/model_build/scenario_abm.modified.json",default,"update_vehicle_technology_choices","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_ML_model_for_battery_discharge","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_TNC_pooling_model","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_external_popsyn_solver","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_heterogenous_VOT","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_ihvs","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_link_based_routing","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_micromobility","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_network_events","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_on_demand_delivery","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_person_gaps","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_retail_electricity_rates","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_router_on_mode_choice","0"
"/tmp/model_build/scenario_abm.modified.json",default,"use_tmc","0"
"/tmp/model_build/scenario_abm.modified.json",default,"utility_based_choice","0"
"/tmp/model_build/scenario_abm.modified.json",default,"vehicle_ownership_reduction","0"
"/tmp/model_build/scenario_abm.modified.json",default,"vehicle_reduction_model_file",""
"/tmp/model_build/scenario_abm.modified.json",default,"vehicle_taking_action","0"
"/tmp/model_build/scenario_abm.modified.json",default,"vehicle_tracking_list_file_name",""
"/tmp/model_build/scenario_abm.modified.json",default,"write_db_input_to_files","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_lc_traffic_trajectory","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_network_link_flow","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_network_link_turn_time","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_network_snapshots","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_node_control_state","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_output_summary","1"
"/tmp/model_build/scenario_abm.modified.json",default,"write_planned_activity_table","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_tnc_acc_egr_time","1"
"/tmp/model_build/scenario_abm.modified.json",default,"write_transit_trajectory","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_ttime_distribution_from_network_model","0"
"/tmp/model_build/scenario_abm.modified.json",default,"write_visualizer_snapshot","1"
"/tmp/model_build/scenario_abm.modified.json",default,"zone_sample_fraction","1.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"activity_generation_model_file","config/choice_models/ActivityGenerationModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"aggregate_routing","1"
"/tmp/model_build/scenario_abm.modified.json",set,"beta_piecewise_linear_fd","2.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"capacity_arterial","1740.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"capacity_expressway","2011.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"capacity_local","1213.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"cav_wtp_model_file","config/choice_models/CAVWTPModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"connectivity_penetration_rate_cv","0.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"convergence_mode","0"
"/tmp/model_build/scenario_abm.modified.json",set,"database_name","Bloomington"
"/tmp/model_build/scenario_abm.modified.json",set,"destination_choice_model_file","config/choice_models/DestinationChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"ecommerce_choice_model_file","config/choice_models/EcommerceChoiceModel_0_08DelRate.json"
"/tmp/model_build/scenario_abm.modified.json",set,"ending_time_hh_mm","24:00"
"/tmp/model_build/scenario_abm.modified.json",set,"enroute_switching_enabled","1"
"/tmp/model_build/scenario_abm.modified.json",set,"enroute_switching_on_excessive_delay","1"
"/tmp/model_build/scenario_abm.modified.json",set,"enroute_switching_use_cv_info","0"
"/tmp/model_build/scenario_abm.modified.json",set,"escooter_use_level_choice_model_file","config/choice_models/EscooterUseLevelChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"fix_connectivity_penetration_rate_cv","0"
"/tmp/model_build/scenario_abm.modified.json",set,"fleet_vehicle_distribution_file_name","fleet_vehicle_distribution.txt"
"/tmp/model_build/scenario_abm.modified.json",set,"flexible_work_percentage","0.130000"
"/tmp/model_build/scenario_abm.modified.json",set,"gamma_piecewise_linear_fd","0.250000"
"/tmp/model_build/scenario_abm.modified.json",set,"generate_transit_skims","0"
"/tmp/model_build/scenario_abm.modified.json",set,"household_transactions_model_file","config/choice_models/HouseholdTransactionsModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"information_compliance_rate_mean","0.250000"
"/tmp/model_build/scenario_abm.modified.json",set,"information_compliance_rate_standard_deviation","0.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"input_highway_skim_file","highway_skim_file.omx"
"/tmp/model_build/scenario_abm.modified.json",set,"input_result_database_name","Bloomington"
"/tmp/model_build/scenario_abm.modified.json",set,"input_transit_skim_file","transit_skim_file.omx"
"/tmp/model_build/scenario_abm.modified.json",set,"ipf_tolerance","0.010000"
"/tmp/model_build/scenario_abm.modified.json",set,"jam_density","200.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"jam_density_constraints_enforced","1"
"/tmp/model_build/scenario_abm.modified.json",set,"lagrangian_coordinates_sub_steps","2"
"/tmp/model_build/scenario_abm.modified.json",set,"marginal_tolerance","5"
"/tmp/model_build/scenario_abm.modified.json",set,"maximum_flow_rate_constraints_enforced","1"
"/tmp/model_build/scenario_abm.modified.json",set,"maximum_iterations","100"
"/tmp/model_build/scenario_abm.modified.json",set,"maximum_link_delay_ratio","160.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"merging_mode","PRIORITY_BASED"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_delay_ratio_for_enroute_switching","3.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_delay_ratio_for_look_ahead_enroute_switching","1.500000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_delay_seconds_for_enroute_switching","600.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_delay_seconds_for_look_ahead_enroute_switching","300.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_link_delay_ratio_for_enroute_switching","4.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_link_delay_seconds_for_enroute_switching","30.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_seconds_from_arrival_for_enroute_switching","150.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_time_between_look_ahead_switches_seconds","600.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_travel_time_saving_mean","1.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"minimum_travel_time_saving_standard_deviation","1.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"mode_choice_model_file","config/choice_models/ModeChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"multiclass_definition","LINK_TYPE_BY_REGULAR_AV_TRUCK"
"/tmp/model_build/scenario_abm.modified.json",set,"multimodal_routing","1"
"/tmp/model_build/scenario_abm.modified.json",set,"multimodal_routing_model_file","MultiModalRoutingModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"node_control_flag","1"
"/tmp/model_build/scenario_abm.modified.json",set,"num_simulation_intervals_per_assignment_interval","60"
"/tmp/model_build/scenario_abm.modified.json",set,"output_directory","Bloomington_iteration_2"
"/tmp/model_build/scenario_abm.modified.json",set,"output_highway_skim_file","highway_skim_file.omx"
"/tmp/model_build/scenario_abm.modified.json",set,"output_link_moe_for_assignment_interval","1"
"/tmp/model_build/scenario_abm.modified.json",set,"output_link_moe_for_simulation_interval","0"
"/tmp/model_build/scenario_abm.modified.json",set,"output_network_moe_for_simulation_interval","0"
"/tmp/model_build/scenario_abm.modified.json",set,"output_transit_skim_file","transit_skim_file.omx"
"/tmp/model_build/scenario_abm.modified.json",set,"output_turn_movement_moe_for_assignment_interval","1"
"/tmp/model_build/scenario_abm.modified.json",set,"output_turn_movement_moe_for_simulation_interval","0"
"/tmp/model_build/scenario_abm.modified.json",set,"percent_to_synthesize","1.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"piecewise_linear_fd","1"
"/tmp/model_build/scenario_abm.modified.json",set,"popsyn_control_file","linker_file.txt"
"/tmp/model_build/scenario_abm.modified.json",set,"pretrip_informed_market_share","0.750000"
"/tmp/model_build/scenario_abm.modified.json",set,"read_population_from_database","1"
"/tmp/model_build/scenario_abm.modified.json",set,"read_skim_tables","1"
"/tmp/model_build/scenario_abm.modified.json",set,"read_trajectories","0"
"/tmp/model_build/scenario_abm.modified.json",set,"read_trip_factors","{ABM:0.000000,External:1.000000}"
"/tmp/model_build/scenario_abm.modified.json",set,"realtime_informed_vehicle_market_share","0.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"replan","{workplaces:0}"
"/tmp/model_build/scenario_abm.modified.json",set,"rideshare_base_fare","3.300000"
"/tmp/model_build/scenario_abm.modified.json",set,"rideshare_cost_per_mile","1.250000"
"/tmp/model_build/scenario_abm.modified.json",set,"rideshare_cost_per_minute","0.250000"
"/tmp/model_build/scenario_abm.modified.json",set,"rng_type","DETERMINISTIC"
"/tmp/model_build/scenario_abm.modified.json",set,"rsu_enabled_switching","0"
"/tmp/model_build/scenario_abm.modified.json",set,"seed","1234567"
"/tmp/model_build/scenario_abm.modified.json",set,"simulate_parking","0"
"/tmp/model_build/scenario_abm.modified.json",set,"simulation_interval_length_in_second","1"
"/tmp/model_build/scenario_abm.modified.json",set,"skim_averaging_factor","0.750000"
"/tmp/model_build/scenario_abm.modified.json",set,"skim_nodes_per_zone","6"
"/tmp/model_build/scenario_abm.modified.json",set,"spacing_shift_by_mode","{BPLATE:0.000000,HD_TRUCK:0.000000,LD_TRUCK:0.000000,MD_TRUCK:0.000000}"
"/tmp/model_build/scenario_abm.modified.json",set,"starting_time_hh_mm","00:00"
"/tmp/model_build/scenario_abm.modified.json",set,"telecommute_choice_model_file","config/choice_models/TelecommuteChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"threshold_conflicting_all_stop_seconds","4.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"threshold_conflicting_signalized_seconds","2.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"threshold_conflicting_two_way_stop_seconds","6.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"time_dependent_routing","1"
"/tmp/model_build/scenario_abm.modified.json",set,"time_dependent_routing","1"
"/tmp/model_build/scenario_abm.modified.json",set,"time_dependent_routing_weight_factor","1.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"time_dependent_routing_weight_scale","900.000000"
"/tmp/model_build/scenario_abm.modified.json",set,"time_dependent_routing_weight_shape","2.500000"
"/tmp/model_build/scenario_abm.modified.json",set,"timing_choice_model_file","config/choice_models/TimingChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"tnc_fleet_model_file","TNCFleetModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"traffic_model","LAGRANGIAN"
"/tmp/model_build/scenario_abm.modified.json",set,"traffic_multiclass_parameters","{av_reaction_multiplier_expressway:0.600000,av_reaction_multiplier_local:1.000000,av_reaction_multiplier_signalized:1.000000,cacc_freight_reaction_time_multiplier_expressway:1.000000,cacc_freight_reaction_time_multiplier_local:1.000000,cacc_freight_reaction_time_multiplier_signalized:1.000000,freight_speed_multiplier_expressway:1.000000,freight_speed_multiplier_local:1.000000,freight_speed_multiplier_signalized:1.000000}"
"/tmp/model_build/scenario_abm.modified.json",set,"traffic_scale_factor","0.250000"
"/tmp/model_build/scenario_abm.modified.json",set,"transit_pass_choice_model_file","config/choice_models/TransitPassChoiceModel.json"
"/tmp/model_build/scenario_abm.modified.json",set,"use_freight_model","0"
"/tmp/model_build/scenario_abm.modified.json",set,"use_realtime_travel_time_for_enroute_switching","0"
"/tmp/model_build/scenario_abm.modified.json",set,"use_tnc_system","1"
"/tmp/model_build/scenario_abm.modified.json",set,"use_traffic_api","0"
"/tmp/model_build/scenario_abm.modified.json",set,"vehicle_distribution_file_name","vehicle_distribution.txt"
"/tmp/model_build/scenario_abm.modified.json",set,"vehicle_trajectory_sample_rate","0.040000"
"/tmp/model_build/scenario_abm.modified.json",set,"write_activity_output","1"
"/tmp/model_build/scenario_abm.modified.json",set,"write_demand_to_database","1"
"/tmp/model_build/scenario_abm.modified.json",set,"write_full_output","0"
"/tmp/model_build/scenario_abm.modified.json",set,"write_marginal_output","0"
"/tmp/model_build/scenario_abm.modified.json",set,"write_skim_tables","1"
"/tmp/model_build/scenario_abm.modified.json",set,"write_vehicle_trajectory","1"
"/tmp/model_build/scenario_abm.modified.json",asserted,"do_skimming","1"
"/tmp/model_build/scenario_abm.modified.json",asserted,"max_acc_meters_per_sec_square","-1.000000"
"/tmp/model_build/scenario_abm.modified.json",asserted,"max_dec_meters_per_sec_square","-1.000000"
"/tmp/model_build/scenario_abm.modified.json",asserted,"num_assignment_intervals_to_toll","1"
"/tmp/model_build/scenario_abm.modified.json",asserted,"use_buildings","0"
"/tmp/model_build/scenario_abm.modified.json",asserted,"use_skim_intervals_from_previous","1"
"MultiModalRoutingModel.json",default,"VOT_freight","60.000000"
"MultiModalRoutingModel.json",default,"alightDuration","1.000000"
"MultiModalRoutingModel.json",default,"boardDuration","2.000000"
"MultiModalRoutingModel.json",default,"dooropencloseDuration","3.000000"
"MultiModalRoutingModel.json",default,"fmlmMinDriveTimeSeconds","180 s"
"MultiModalRoutingModel.json",default,"fmlmProportion","2.000000"
"MultiModalRoutingModel.json",default,"multimodal_dijkstra_walk_to_transit","0"
"MultiModalRoutingModel.json",default,"multimodal_dijkstra_walk_truncated","0"
"MultiModalRoutingModel.json",default,"tncWaitCountThreshold","2.000000"
"MultiModalRoutingModel.json",default,"tncWeight","5.000000"
"MultiModalRoutingModel.json",default,"transitWaitCountThreshold","5.000000"
"MultiModalRoutingModel.json",set,"VOT","18.000000"
"MultiModalRoutingModel.json",set,"bikeSpeed","16 kph"
"MultiModalRoutingModel.json",set,"bikeThreshold","10000 m"
"MultiModalRoutingModel.json",set,"bikeWeight","3.000000"
"MultiModalRoutingModel.json",set,"capacityAlpha","80"
"MultiModalRoutingModel.json",set,"capacityBeta","0.600000"
"MultiModalRoutingModel.json",set,"carWeight","15.000000"
"MultiModalRoutingModel.json",set,"commuter_rail_costThreshold","43200"
"MultiModalRoutingModel.json",set,"costThreshold","28800.000000"
"MultiModalRoutingModel.json",set,"ivtWeight","1.000000"
"MultiModalRoutingModel.json",set,"multimodal_dijkstra","1"
"MultiModalRoutingModel.json",set,"multimodal_dijkstra_drive","0"
"MultiModalRoutingModel.json",set,"rail_bikeThreshold","15000 m"
"MultiModalRoutingModel.json",set,"rail_bikeWeight","2.000000"
"MultiModalRoutingModel.json",set,"rail_ivtWeight","0.800000"
"MultiModalRoutingModel.json",set,"rail_ivtWeight_ampeak","0.800000"
"MultiModalRoutingModel.json",set,"rail_ivtWeight_pmpeak","0.800000"
"MultiModalRoutingModel.json",set,"rail_waitThreshold","2700 s"
"MultiModalRoutingModel.json",set,"rail_waitWeight","2.000000"
"MultiModalRoutingModel.json",set,"rail_walkThreshold","3750 m"
"MultiModalRoutingModel.json",set,"rail_walkWeight","2.660000"
"MultiModalRoutingModel.json",set,"real_time_load_estimation","0"
"MultiModalRoutingModel.json",set,"run_buses_in_traffic","0"
"MultiModalRoutingModel.json",set,"scanThreshold","75000.000000"
"MultiModalRoutingModel.json",set,"standWeight","0.500000"
"MultiModalRoutingModel.json",set,"transferPenalty","1020 s"
"MultiModalRoutingModel.json",set,"waitThreshold","1800 s"
"MultiModalRoutingModel.json",set,"waitWeight","3.000000"
"MultiModalRoutingModel.json",set,"walkSpeed","5 kph"
"MultiModalRoutingModel.json",set,"walkThreshold","2500 m"
"MultiModalRoutingModel.json",set,"walkWeight","4.000000"
"config/choice_models/ModeChoiceModel.json",default,"B_Transit_at_capacity_ratio","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_ASC_TNC_AND_RIDE","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_ASC_XitWlk_Sub","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_bCost_tncXit","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_bIVTT_tncAcEg","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_bInitWait_TransferTime_XitDrv","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBO_bInitWait_TransferTime_tncXit","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBW_ASC_TNC_AND_RIDE","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBW_ASC_XitWlk_Sub","2.680075"
"config/choice_models/ModeChoiceModel.json",default,"HBW_bCost_tncXit","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBW_bIVTT_tncAcEg","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"HBW_bInitWait_TransferTime_tncXit","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"NHB_ASC_TNC_AND_RIDE","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"NHB_ASC_XitWlk_Sub","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"NHB_bIVTT_tncAcEg","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"NHB_bInitWait_TransferTime_tncXit","0.000000"
"config/choice_models/ModeChoiceModel.json",default,"bTT_multiplier_suburb","1.000000"
"config/choice_models/ModeChoiceModel.json",set,"B_Density_Taxi","2.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_Auto","-1.559870"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_Bike","-17.845278"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_Getride","-1.432091"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_RailDrv","-21.919626"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_RailWlk","-20.760880"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_Taxi","-20.999327"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_Walk","22.559525"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_XitDrv","-10.886173"
"config/choice_models/ModeChoiceModel.json",set,"HBO_ASC_XitWlk","3.802789"
"config/choice_models/ModeChoiceModel.json",set,"HBO_NEST_AUTO","0.393701"
"config/choice_models/ModeChoiceModel.json",set,"HBO_NEST_BIKE_AND_PNR","1.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_NEST_RAIL","0.714286"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_Change_Mode_Drive","-1.330000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_Change_Mode_XitWlk","-1.170000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_Pickup_or_Dropoff_Pass_Drive","1.520000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_Pickup_or_Dropoff_Pass_GetRide","0.319000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_Shop_XitWlk","-0.457000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_SocRec_Bike","0.643000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_SocRec_GetRide","0.618000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_SocRec_Walk","0.347000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_Purp_SocRec_XitWlk","-0.284000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_Auto_Acc_Egr_XitDrv","-1.890000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_drive_InMotion","-1.490000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_drive_Parking","-0.648000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_railDrv","-2.240000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_railWlk","-2.750000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_taxi","-2.734796"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_xitDrv","-1.830000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bCost_xitWlk","-1.830000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_AcEgT_XitDrv","-0.032200"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_AcEgT_XitWlk","-0.032200"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_OVTTAcEg_Rail","-0.014300"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_OVTTAcEg_RailDrv","-0.014300"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_drive","-0.060100"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_getRide","-0.063600"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bIVTT_taxi","-0.463939"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bInitWait_TransferTime_Rail","-0.022500"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bInitWait_TransferTime_RailDrv","-0.022500"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bInitWait_TransferTime_XitWlk","-0.064400"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bTotalTime_XitDrv","-0.032200"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bTotalTime_XitDrv","-0.032200"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_ActivityDensityAtDestBG_Drive","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_ActivityDensityAtDestBG_Rail","1.010000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_ActivityDensityAtDestBG_Walk","1.440000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_ActivityDensityAtDestBG_XitWlk","0.944000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_AssocPlus_XitWlk","-0.763000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_BachPlus_GetRide","-0.305000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_Bach_Bike","0.227000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_Bach_Taxi","0.365000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_Bach_Walk","0.476000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_GradProf_Bike","0.501000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_GradProf_RailWlk","0.517000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_GradProf_Taxi","0.280000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_GradProf_Walk","0.565000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_LtHS_GetRide","0.599000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Edu_LtHS_XitWlk","1.100000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Female_GetRide","-0.988000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Female_XitWlk","0.353000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize1_GetRide","-1.630000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize_Bike","-0.172000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize_GetRide","-0.106000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize_RailWlk","0.113000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize_Taxi","0.170000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHSize_XitWlk","0.348000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_Bike","-0.598000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_Drive","0.094900"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_RailWlk","-0.524000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_Taxi","-0.597000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_Walk","-0.590000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_HHVEH_XitWlk","-2.890000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_NoDL_Bike","2.800000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_NoDL_GetRide","3.370000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_NoDL_RailWlk","2.540000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_NoDL_Taxi","1.410000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_NoDL_Walk","3.160000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_OwnSF_Drive","0.108000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_OwnSF_RailWlk","-0.321000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_OwnSF_Taxi","-0.340000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_OwnSF_Walk","-0.504000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_OwnSF_XitWlk","-0.490000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_Bike","3.850000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_Drive","-1.670000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_RailDrv","2.350000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_RailWlk","1.380000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_Ride","-1.900000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_RoadNtwkDensityAtDestBG_Walk","4.190000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_Bike","0.495000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_GetRide","0.145000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_RailDrv","0.759000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_RailWlk","1.030000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_Taxi","-0.555000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_Student_XitWlk","1.110000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_b_nBikes_Bike","0.528000"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bnmBikeTime_Bike","-0.058300"
"config/choice_models/ModeChoiceModel.json",set,"HBO_bnmWalkTime_Walk","-0.136000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_Auto","-0.809053"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_Bike","-11.055036"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_Getride","-17.662947"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_RailDrv","-3.744106"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_RailWlk","0.725938"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_Taxi","3.482542"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_Walk","15.027607"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_XitDrv","-4.974521"
"config/choice_models/ModeChoiceModel.json",set,"HBW_ASC_XitWlk","2.680075"
"config/choice_models/ModeChoiceModel.json",set,"HBW_NEST_AUTO","1.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_NEST_BIKE_AND_PNR","1.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_NEST_RAIL","1.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_Auto_Acc_Egr_XitDrv","-0.657000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_Auto_Acc_Egr_XitDrv","-0.657000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_Auto_Acc_Egr_XitDrv","-0.657000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_Generic","-1.340000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_drive_InMotion","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_drive_Parking","-0.781000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_railDrv","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_railWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_taxi","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bCost_xitWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_AcEgT_XitDrv","-0.031400"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_AcEgT_XitWlk","-0.031400"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_OVTTAcEg_Rail","-0.018600"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_OVTTAcEg_RailDrv","-0.018600"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_drive","-0.064700"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_getRide","-0.090100"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bIVTT_taxi","-1.156082"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bInitWait_TransferTime_Rail","-0.037200"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bInitWait_TransferTime_RailDrv","-0.037200"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bInitWait_TransferTime_XitDrv","-0.062800"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bInitWait_TransferTime_XitWlk","-0.062800"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bNumberOfTransfers_XitDrv","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bTotalTime_Rail","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bTotalTime_XitDrv","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_ActivityDensityAtDestBG_Bike","0.751000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_ActivityDensityAtDestBG_Drive","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_ActivityDensityAtDestBG_Rail","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_ActivityDensityAtDestBG_Taxi","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_ActivityDensityAtDestBG_Walk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_AssocPlus_XitWlk","-0.642000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_BachPlus_GetRide","-0.217000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_Bach_Bike","0.323000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_Bach_Taxi","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_Bach_Walk","0.719000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_GradProf_Bike","0.547000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_GradProf_RailWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_GradProf_Taxi","0.445000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_GradProf_Walk","0.524000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_LtHS_GetRide","1.040000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Edu_LtHS_XitWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Female_GetRide","-0.538000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Female_XitWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHSize1_GetRide","-1.940000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHSize_Bike","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHSize_GetRide","0.088800"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHSize_RailWlk","0.200000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHSize_XitWlk","0.404000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_Bike","-0.671000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_Drive","0.462000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_RailWlk","-0.960000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_Taxi","-1.330000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_Walk","-0.319000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_HHVEH_XitWlk","-2.050000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_NoDL_Bike","1.510000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_NoDL_GetRide","3.010000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_NoDL_RailWlk","1.010000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_NoDL_Taxi","1.810000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_NoDL_Walk","3.110000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_OwnSF_RailWlk","-0.283000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_OwnSF_Taxi","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_OwnSF_Walk","-0.892000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_OwnSF_XitWlk","-0.430000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_RoadNtwkDensityAtDestBG_Drive","-3.500000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_RoadNtwkDensityAtDestBG_RailDrv","1.080000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_RoadNtwkDensityAtDestBG_RailWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_RoadNtwkDensityAtDestBG_Ride","-1.720000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_RoadNtwkDensityAtDestBG_Walk","3.630000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_Bike","0.930000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_GetRide","0.342000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_RailDrv","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_RailWlk","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_Taxi","0.000000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_Student_XitWlk","0.228000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_b_nBikes_Bike","0.501000"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bnmBikeTime_Bike","-0.039800"
"config/choice_models/ModeChoiceModel.json",set,"HBW_bnmWalkTime_Walk","-0.107000"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_Auto","1.784319"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_Bike","-18.964567"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_Getride","-16.678278"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_RailDrv","180.260574"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_RailWlk","-17.581306"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_Taxi","-28.431999"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_Walk","22.675489"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_XitDrv","158.706421"
"config/choice_models/ModeChoiceModel.json",set,"NHB_ASC_XitWlk","-11.444543"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_EAT_OUT_ACTIVITY_FREQ","0.149021"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_ERRANDS_ACTIVITY_FREQ","0.090709"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_HEALTHCARE_ACTIVITY_FREQ","0.055025"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_LEISURE_ACTIVITY_FREQ","0.120846"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_MAJOR_SHOPPING_ACTIVITY_FREQ","0.043183"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_OTHER_SHOPPING_ACTIVITY_FREQ","0.178868"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.036955"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.021493"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_SCHOOL_ACTIVITY_FREQ","0.543694"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_SERVICE_VEHICLE_ACTIVITY_FREQ","0.060441"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_SOCIAL_ACTIVITY_FREQ","0.112630"
"config/choice_models/ActivityGenerationModel.json",set,"ADULT_STUDENT_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_EAT_OUT_ACTIVITY_FREQ","0.224431"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_ERRANDS_ACTIVITY_FREQ","0.097269"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_HEALTHCARE_ACTIVITY_FREQ","0.072242"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_LEISURE_ACTIVITY_FREQ","0.147659"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_MAJOR_SHOPPING_ACTIVITY_FREQ","0.084879"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_OTHER_SHOPPING_ACTIVITY_FREQ","0.331660"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.052576"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.037363"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_SCHOOL_ACTIVITY_FREQ","0.216524"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_SERVICE_VEHICLE_ACTIVITY_FREQ","0.085229"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_SOCIAL_ACTIVITY_FREQ","0.146565"
"config/choice_models/ActivityGenerationModel.json",set,"FULLTIME_WORKER_WORK_ACTIVITY_FREQ","1.304511"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_EAT_OUT_ACTIVITY_FREQ","0.149898"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_ERRANDS_ACTIVITY_FREQ","0.168352"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_HEALTHCARE_ACTIVITY_FREQ","0.113548"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_LEISURE_ACTIVITY_FREQ","0.133569"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_MAJOR_SHOPPING_ACTIVITY_FREQ","0.085115"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_OTHER_SHOPPING_ACTIVITY_FREQ","0.409024"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.029523"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.038224"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_SCHOOL_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_SERVICE_VEHICLE_ACTIVITY_FREQ","0.049959"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_SOCIAL_ACTIVITY_FREQ","0.121019"
"config/choice_models/ActivityGenerationModel.json",set,"NONWORKER_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_EAT_OUT_ACTIVITY_FREQ","0.278920"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_ERRANDS_ACTIVITY_FREQ","0.165020"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_HEALTHCARE_ACTIVITY_FREQ","0.122665"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_LEISURE_ACTIVITY_FREQ","0.201311"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_MAJOR_SHOPPING_ACTIVITY_FREQ","0.091481"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_OTHER_SHOPPING_ACTIVITY_FREQ","0.482886"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_PART_TIME_WORK_ACTIVITY_FREQ","0.722869"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.069059"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.050200"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_SCHOOL_ACTIVITY_FREQ","0.600389"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_SERVICE_VEHICLE_ACTIVITY_FREQ","0.091336"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_SOCIAL_ACTIVITY_FREQ","0.232520"
"config/choice_models/ActivityGenerationModel.json",set,"PARTTIME_WORKER_WORK_ACTIVITY_FREQ","-0.033913"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_EAT_OUT_ACTIVITY_FREQ","0.018864"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_ERRANDS_ACTIVITY_FREQ","0.008814"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_HEALTHCARE_ACTIVITY_FREQ","0.007756"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_LEISURE_ACTIVITY_FREQ","0.033642"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_MAJOR_SHOPPING_ACTIVITY_FREQ","0.004872"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_OTHER_SHOPPING_ACTIVITY_FREQ","0.043607"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.013636"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_SCHOOL_ACTIVITY_FREQ","0.026560"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_SERVICE_VEHICLE_ACTIVITY_FREQ","0.001423"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_SOCIAL_ACTIVITY_FREQ","0.000435"
"config/choice_models/ActivityGenerationModel.json",set,"PRESCHOOL_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_EAT_OUT_ACTIVITY_FREQ","0.166029"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_ERRANDS_ACTIVITY_FREQ","0.040610"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_HEALTHCARE_ACTIVITY_FREQ","0.046117"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_LEISURE_ACTIVITY_FREQ","0.308747"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_MAJOR_SHOPPING_ACTIVITY_FREQ","0.021560"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_OTHER_SHOPPING_ACTIVITY_FREQ","0.140684"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.010620"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.066672"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_SCHOOL_ACTIVITY_FREQ","0.918171"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_SERVICE_VEHICLE_ACTIVITY_FREQ","0.020981"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_SOCIAL_ACTIVITY_FREQ","0.216382"
"config/choice_models/ActivityGenerationModel.json",set,"SCHOOL_CHILD_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_EAT_OUT_ACTIVITY_FREQ","0.202159"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_ERRANDS_ACTIVITY_FREQ","0.234877"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_HEALTHCARE_ACTIVITY_FREQ","0.159474"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_LEISURE_ACTIVITY_FREQ","0.214060"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_MAJOR_SHOPPING_ACTIVITY_FREQ","0.085888"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_OTHER_SHOPPING_ACTIVITY_FREQ","0.625699"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.038951"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.069134"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_SCHOOL_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_SERVICE_VEHICLE_ACTIVITY_FREQ","0.065365"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_SOCIAL_ACTIVITY_FREQ","0.182939"
"config/choice_models/ActivityGenerationModel.json",set,"SENIOR_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_EAT_OUT_ACTIVITY_FREQ","0.115514"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_ERRANDS_ACTIVITY_FREQ","0.020990"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_HEALTHCARE_ACTIVITY_FREQ","0.020916"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_LEISURE_ACTIVITY_FREQ","0.119047"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_MAJOR_SHOPPING_ACTIVITY_FREQ","0.011699"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_OTHER_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_OTHER_SHOPPING_ACTIVITY_FREQ","0.044306"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_PART_TIME_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_PERSONAL_BUSINESS_ACTIVITY_FREQ","0.005777"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_RELIGIOUS_OR_CIVIC_ACTIVITY_FREQ","0.014581"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_SCHOOL_ACTIVITY_FREQ","0.869513"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_SERVICE_VEHICLE_ACTIVITY_FREQ","0.010738"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_SOCIAL_ACTIVITY_FREQ","0.081720"
"config/choice_models/ActivityGenerationModel.json",set,"STUDENT_DRIVER_WORK_ACTIVITY_FREQ","0.000000"
"config/choice_models/DestinationChoiceModel.json",default,"BLuArea_Related_WORK","0.030000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_CIVIC","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_EAT_OUT","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_ERRANDS","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_HEALTHCARE","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_LEISURE","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_MAJ_SHOP","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_MIN_SHOP","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_OTHER","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_OTHER_WORK","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_PERSONAL","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_PICK","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_SCHOOL","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_SERVICE","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_SOCIAL","1.000000"
"config/choice_models/DestinationChoiceModel.json",default,"C_DISTANCE_WORK","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_LEISURE","2.285000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_OTHER_WORK","1.049801"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_PICK","0.271475"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArEnt_WORK","0.044300"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_CIVIC","0.028000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_LEISURE","0.035000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_MIN_SHOP","0.033000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_OTHER_WORK","0.236772"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_PICK","0.167705"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_SERVICE","0.061000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_SOCIAL","0.150000"
"config/choice_models/DestinationChoiceModel.json",set,"BArIns_WORK","0.053900"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_LEISURE","0.712000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_MIN_SHOP","0.075000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_OTHER_WORK","0.470220"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_PICK","0.817500"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_SERVICE","0.341000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_SOCIAL","0.717000"
"config/choice_models/DestinationChoiceModel.json",set,"BArMix_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_OTHER_WORK","0.249596"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_PICK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArOff_WORK","-0.006370"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_LEISURE","0.017000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_MIN_SHOP","0.011000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_OTHER","0.490179"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_OTHER_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_PICK","0.027283"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_SERVICE","0.016000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_SOCIAL","0.043000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRec_WORK","0.021800"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_CIVIC","0.108000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_OTHER","1.021581"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_OTHER_WORK","0.128388"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_PICK","0.204766"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_SOCIAL","0.091000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRes_WORK","-0.030400"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_EAT_OUT","4.241000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_LEISURE","0.621000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_MAJ_SHOP","4.475000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_MIN_SHOP","4.140000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_OTHER","6.690560"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_OTHER_WORK","1.028168"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_PICK","0.544154"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_SERVICE","0.466000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_SOCIAL","0.491000"
"config/choice_models/DestinationChoiceModel.json",set,"BArRet_WORK","0.028700"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_CIVIC","0.986000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_LEISURE","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_OTHER","0.753482"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_OTHER_WORK","0.815026"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_PICK","0.416465"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_SERVICE","0.348000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_SOCIAL","0.305000"
"config/choice_models/DestinationChoiceModel.json",set,"BArSch_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_CIVIC","1.547000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_LEISURE","0.066000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_OTHER_WORK","0.464937"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_PICK","0.867538"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_SERVICE","0.527000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_SOCIAL","1.403000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmGov_WORK","0.304000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_OTHER_WORK","0.250782"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_PICK","0.320339"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmInd_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_OTHER","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_OTHER_WORK","0.363034"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_PICK","0.516197"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmMan_WORK","0.438000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_EAT_OUT","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_MAJ_SHOP","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_OTHER_WORK","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_PICK","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmOth_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_EAT_OUT","2.815000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_MAJ_SHOP","1.085000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_MIN_SHOP","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_OTHER_WORK","0.419426"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_PICK","0.625740"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_SERVICE","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_SOCIAL","0.942000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmRet_WORK","0.476000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_CIVIC","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_OTHER_WORK","0.768692"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_PICK","0.765281"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_SERVICE","0.966000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_SOCIAL","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmSer_WORK","0.108000"
"config/choice_models/DestinationChoiceModel.json",set,"BEmUnrelated_WORK","0.040400"
"config/choice_models/DestinationChoiceModel.json",set,"BHOME_WORK","2.230000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_CIVIC","-0.092000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_EAT_OUT","-0.056000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_LEISURE","-0.046000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_MIN_SHOP","-0.027000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_OTHER","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_OTHER_WORK","0.038861"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_PICK","-0.027821"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_SERVICE","-0.070000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_SOCIAL","-0.058000"
"config/choice_models/DestinationChoiceModel.json",set,"BINCD_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_CIVIC","-2.009000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_EAT_OUT","-1.139000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_LEISURE","-1.325000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_MIN_SHOP","-0.844000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_OTHER","-0.297174"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_OTHER_WORK","-0.025711"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_PICK","-0.660094"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_SERVICE","-1.027000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_SOCIAL","-0.969000"
"config/choice_models/DestinationChoiceModel.json",set,"BRACED_WORK","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"BTTAUTO_WORK","-0.110000"
"config/choice_models/DestinationChoiceModel.json",set,"BTTOTHER_WORK","-0.038600"
"config/choice_models/DestinationChoiceModel.json",set,"BTTTRAN_WORK","-0.067700"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_CIVIC","-0.076000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_EAT_OUT","-0.067000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_LEISURE","-0.062000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_MAJ_SHOP","-0.062000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_MIN_SHOP","-0.075000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_OTHER","-0.150126"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_OTHER_WORK","-0.028552"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_PICK","-0.070956"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_SERVICE","-0.060000"
"config/choice_models/DestinationChoiceModel.json",set,"BTT_SOCIAL","-0.059000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_CIVIC","0.648000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_EAT_OUT","0.274000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_LEISURE","0.582000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_MAJ_SHOP","0.381000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_MIN_SHOP","0.434000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_OTHER","1.000000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_OTHER_WORK","0.659908"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_PICK","0.806092"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_SERVICE","0.635000"
"config/choice_models/DestinationChoiceModel.json",set,"GAMMA_SOCIAL","0.979000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_CIVIC","0.201000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_MIN_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_OTHER","-0.014155"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_OTHER_WORK","0.164304"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_PICK","0.137468"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_SOCIAL","0.190000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAG_WORK","0.073400"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_CIVIC","-0.711000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_EAT_OUT","-0.255000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_LEISURE","-0.619000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_MIN_SHOP","-0.385000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_OTHER","0.331496"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_OTHER_WORK","-0.160350"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_PICK","0.084240"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_SERVICE","-0.126000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAI_WORK","0.220000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_EAT_OUT","-0.296000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_LEISURE","-0.420000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_MIN_SHOP","-0.326000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_OTHER","-1.168986"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_OTHER_WORK","-0.225686"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_PICK","-0.516313"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_SERVICE","-0.407000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_SOCIAL","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAM_WORK","0.184000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_EAT_OUT","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_MAJ_SHOP","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_MIN_SHOP","0.400000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_OTHER","0.732551"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_OTHER_WORK","-0.333137"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_PICK","-0.308240"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_SOCIAL","-1.228000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAO_WORK","0.244000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_EAT_OUT","-0.223000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_LEISURE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_MAJ_SHOP","0.355000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_MIN_SHOP","0.191000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_OTHER","-0.339726"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_OTHER_WORK","-0.108240"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_PICK","0.027511"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_SERVICE","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_SOCIAL","0.285000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAR_WORK","-0.026600"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_CIVIC","0.000000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_EAT_OUT","0.056000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_LEISURE","0.093000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_MAJ_SHOP","-0.055000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_MIN_SHOP","-0.032000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_OTHER","0.006282"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_OTHER_WORK","0.048763"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_PICK","0.011992"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_SERVICE","0.029000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_SOCIAL","0.051000"
"config/choice_models/DestinationChoiceModel.json",set,"THETAS_WORK","0.006860"
"config/choice_models/DestinationChoiceModel.json",set,"THETA_UR_WORK","-0.008310"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_INCOME_HIGH","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_MODE_AUTO","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_TELEWORK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMOFFPEAK_WORK_FULLTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMPEAK_FLEX_DURATION","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMPEAK_TELEWORK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_AMPEAK_WORK_FULLTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_EVENING_AGE_60","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_EVENING_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_EVENING_MODE_TRANSIT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_EVENING_WORK_FULLTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_NIGHT_INCOME_HIGH","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_NIGHT_LOCATION_SUBURBS","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMOFFPEAK_AGE_18_24","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMOFFPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMOFFPEAK_HH_SIZE","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMOFFPEAK_MODE_PASSENGER","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMOFFPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMPEAK_AGE_60","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"D_PMPEAK_PARTY_ALONE","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_AMOFFPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_AMPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_EVENING","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_NIGHT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_PMOFFPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"SIGMA_PMPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_MODE_PASSENGER","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_STUDENT_FULLTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_TT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_TTV","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMOFFPEAK_WORK_PARTTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_AGE_60","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_FLEX_DURATION","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_FLEX_START","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_HH_WORKERS","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_PARTY_JOINT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_AMPEAK_TT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_EVENING_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_EVENING_DEGREE_COLLEGE","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_EVENING_INCOME_LOW","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_EVENING_TELEWORK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_EVENING_TT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMOFFPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMOFFPEAK_INCOME_LOW","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMOFFPEAK_MODE_TRANSIT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMOFFPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMOFFPEAK_TT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_CONSTANT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_FLEX_START","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_HH_WORKERS","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_INCOME_HIGH","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_OCCUPANCY","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_TT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"S_PMPEAK_WORK_FULLTIME","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_AMOFFPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_AMPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_EVENING","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_NIGHT","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_PMOFFPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",default,"THETA_PMPEAK","0.000000"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_EAT_OUT","10.817589"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_ERRANDS","11.838671"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_HEALTHCARE","11.845022"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_LEISURE","10.454590"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_PERSONAL","11.452014"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_RELIGIOUS","9.523056"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_SERVICE","10.936163"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_SHOP_MAJOR","11.433891"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_SHOP_OTHER","12.129076"
"config/choice_models/TimingChoiceModel.json",set,"C_AMOFFPEAK_SOCIAL","10.447537"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_EAT_OUT","2.650289"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_ERRANDS","3.700870"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_HEALTHCARE","4.084024"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_LEISURE","3.156337"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_PERSONAL","4.056079"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_RELIGIOUS","2.851032"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_SERVICE","3.924811"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_SHOP_MAJOR","1.496398"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_SHOP_OTHER","4.000711"
"config/choice_models/TimingChoiceModel.json",set,"C_AMPEAK_SOCIAL","2.676526"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_EAT_OUT","3.323382"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_ERRANDS","3.167460"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_HEALTHCARE","-1.716603"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_LEISURE","3.228072"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_PERSONAL","2.778199"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_RELIGIOUS","1.997029"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_SERVICE","3.570788"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_SHOP_MAJOR","3.447624"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_SHOP_OTHER","4.088496"
"config/choice_models/TimingChoiceModel.json",set,"C_EVENING_SOCIAL","3.725029"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_EAT_OUT","-2.539964"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_ERRANDS","-28.744537"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_HEALTHCARE","-1.827507"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_LEISURE","-1.458526"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_PERSONAL","-0.708677"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_RELIGIOUS","-3.069638"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_SERVICE","-1.044420"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_SHOP_MAJOR","-3.830547"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_SHOP_OTHER","-2.758219"
"config/choice_models/TimingChoiceModel.json",set,"C_NIGHT_SOCIAL","-0.557393"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_EAT_OUT","7.566102"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_ERRANDS","8.449842"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_HEALTHCARE","8.231187"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_LEISURE","7.142060"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_PERSONAL","8.133667"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_RELIGIOUS","5.993411"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_SERVICE","7.965273"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_SHOP_MAJOR","8.580468"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_SHOP_OTHER","9.030624"
"config/choice_models/TimingChoiceModel.json",set,"C_PMOFFPEAK_SOCIAL","7.641714"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_EAT_OUT","7.523941"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_ERRANDS","7.798015"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_HEALTHCARE","7.672511"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_LEISURE","7.598394"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_PERSONAL","7.734021"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_RELIGIOUS","7.533160"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_SERVICE","7.807472"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_SHOP_MAJOR","7.898048"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_SHOP_OTHER","8.736084"
"config/choice_models/TimingChoiceModel.json",set,"C_PMPEAK_SOCIAL","7.640666"
"config/choice_models/TimingChoiceModel.json",set,"START_DURATION_DISTRIBUTION_FILE","start_time_duration_data.txt"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_ACT_DENS_SUBURB","0.181259"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_AGE_16_24","-0.475422"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_COMMUTE_AUTO","-0.540294"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_CONSTANT","0.290000"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_EDUC_BACHELOR","0.161198"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_FEMALE","0.061943"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_FLEX_WORK_INDICATOR","0.983207"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_HIGH_COMM_DIST","0.013771"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_INCOME_HIGH","0.193789"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_ADMIN","0.307132"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_ENTERTAIN","-0.166691"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_FINANCE","0.270902"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_HEALTH","-0.217744"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_INFORMATION","0.505476"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_PROFESSIONAL","0.266225"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_IND_TRANSPORT","-0.374352"
"config/choice_models/TelecommuteChoiceModel.json",set,"O_NTWRK_DENS","-0.003984"
"config/choice_models/TelecommuteChoiceModel.json",set,"T_C1","-0.084386"
"config/choice_models/TelecommuteChoiceModel.json",set,"T_C2","-0.934117"
"config/choice_models/TelecommuteChoiceModel.json",set,"T_C3","-1.350351"
"config/choice_models/TelecommuteChoiceModel.json",set,"T_C4","-1.961020"
"config/choice_models/TelecommuteChoiceModel.json",set,"T_RHO","0.152041"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_AGE_35_54","0.252522"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_COMMUTE_AUTO","-0.404461"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_CONSTANT","-0.700000"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_EDUC_GRAD","1.511662"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_EDUC_LESS_HS","-0.477710"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_FEMALE","0.182772"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_HIGH_COMM_TIME","0.280676"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_INCOME_LOW","-1.371125"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_IND_CONSTRUCTION","-0.878060"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_IND_FINANCE","1.178004"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_IND_RETAIL","-0.650627"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_IND_SERVICE","-0.648987"
"config/choice_models/TelecommuteChoiceModel.json",set,"Z_PCT_WORK_POP","1.361042"
"config/choice_models/CAVWTPModel.json",set,"B_FACTOR_ADVANCED_MOBILITY","1.780000"
"config/choice_models/CAVWTPModel.json",set,"B_FACTOR_ENJOY_DRIVE","-0.210000"
"config/choice_models/CAVWTPModel.json",set,"B_FACTOR_ENVIRONMENTAL","0.280000"
"config/choice_models/CAVWTPModel.json",set,"B_FACTOR_PUBLIC_TRANSIT","0.110000"
"config/choice_models/CAVWTPModel.json",set,"B_FUEL_EFFICIENCY_EXP_IMP","-1.780000"
"config/choice_models/CAVWTPModel.json",set,"B_HAD_MAJOR_ACCIDENT","1.200000"
"config/choice_models/CAVWTPModel.json",set,"B_HAS_VEHICLE","-1.860000"
"config/choice_models/CAVWTPModel.json",set,"B_HIGH_PRICE_EXP_IMP","1.510000"
"config/choice_models/CAVWTPModel.json",set,"B_IMPERFECT_PERFORMANCE_EXP_IMP","1.970000"
"config/choice_models/CAVWTPModel.json",set,"B_JOB_ADMIN","-0.640000"
"config/choice_models/CAVWTPModel.json",set,"B_LESS_STRESSFUL_EXP","0.850000"
"config/choice_models/CAVWTPModel.json",set,"B_LN_VMT","0.190000"
"config/choice_models/CAVWTPModel.json",set,"B_LONG_DISTANCE_TRIPS","0.430000"
"config/choice_models/CAVWTPModel.json",set,"B_NO_TELECOMMUTE","-0.960000"
"config/choice_models/CAVWTPModel.json",set,"B_TRANSIT_ACCESSIBLE","-0.180000"
"config/choice_models/CAVWTPModel.json",set,"B_WANTS_AV_TO_WORK","1.650000"
"config/choice_models/CAVWTPModel.json",set,"CONSTANT","2.300000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_CONTROL_FAILURE_AVG","0.070000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_FUEL_EFFICIENCY_AVG","0.350000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_HIGH_PRICE_AVG","0.200000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_IMPERFECT_PERFORMANCE_AVG","0.070000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_INCREASED_SAFETY_AVG","0.090000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_LESS_STRESSFUL_AVG","0.450000"
"config/choice_models/CAVWTPModel.json",set,"EXPECT_PRIVACY_BREACH","0.630000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_ADVANCED_MOBILITY_AVG","0.030000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_ADVANCED_MOBILITY_STD","1.700000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_AV_INTEREST_AVG","0.060000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_AV_INTEREST_STD","1.390000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_DRIVE_THRILL_AVG","-0.100000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_DRIVE_THRILL_STD","0.860000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_ENVIRONMENT_AVG","-0.200000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_ENVIRONMENT_STD","2.230000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_TRANSIT_AVG","0.290000"
"config/choice_models/CAVWTPModel.json",set,"FACTOR_TRANSIT_STD","1.650000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_EDUC_LOW","-0.210000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FACTOR_ADVANCED_MOBILITY","1.700000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FACTOR_AV_INTEREST","0.380000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FACTOR_ENVIRONMENTAL","0.330000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FACTOR_PUBLIC_TRANSIT","0.210000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FAILURE_UNLIKELY_EXP_IMP","2.390000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_FUEL_EFFICIENCY_EXP_IMP","-0.290000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_HAD_MAJOR_ACCIDENT","0.550000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_HAS_VEHICLE","-0.410000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_HHSIZE_OVER5","1.930000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_HIGHPRICE_UNLIKELY_EXP_IMP","0.620000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_IMPERFECT_PERFORMANCE_EXP_IMP","1.580000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_INCOME_HIGH","1.350000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_LESS_STRESSFUL_EXP","1.240000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_LONG_DISTANCE_TRIPS","0.670000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_NO_TELECOMMUTE","-0.580000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_PRIVACY_IMP","-0.560000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_SAFETY_EXP_IMP","1.660000"
"config/choice_models/CAVWTPModel.json",set,"L5_B_WANTS_AV_TO_WORK","0.680000"
"config/choice_models/CAVWTPModel.json",set,"L5_CONSTANT","2.470000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_1","0.740000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_1_STD","1.310000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_2","0.850000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_2_STD","1.780000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_3","0.530000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_3_STD","1.280000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_4","1.390000"
"config/choice_models/CAVWTPModel.json",set,"L5_MU_4_STD","1.710000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_CONSTANT","1.880000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_EDUC_LOW","0.520000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_FACTOR_AV_INTEREST","0.300000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_FACTOR_ENVIRONMENT","0.380000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_FACTOR_PUBLIC_TRANSIT","0.390000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_FAILURE_UNLIKELY_EXP_IMP","3.610000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_FUEL_EFFICIENCY_EXP_IMP","1.320000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_HAS_VEHICLE","1.520000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_HHSIZE_OVER5","2.330000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_HIGHPRICE_UNLIKELY_EXP_IMP","0.990000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_INCOME_HIGH","1.410000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_LONG_DISTANCE_TRIPS","0.840000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_MAJOR_ACCIDENT","0.730000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_NO_TELECOMMUTE","0.610000"
"config/choice_models/CAVWTPModel.json",set,"L5_STD_PRIVAY_IMP","0.950000"
"config/choice_models/CAVWTPModel.json",set,"L5_T_DIST_HOME_WORK_OVER15","-0.490000"
"config/choice_models/CAVWTPModel.json",set,"L5_T_LN_VMT","-0.050000"
"config/choice_models/CAVWTPModel.json",set,"MU_1","0.370000"
"config/choice_models/CAVWTPModel.json",set,"MU_1_STD","1.160000"
"config/choice_models/CAVWTPModel.json",set,"MU_2","0.540000"
"config/choice_models/CAVWTPModel.json",set,"MU_2_STD","1.550000"
"config/choice_models/CAVWTPModel.json",set,"MU_3","0.190000"
"config/choice_models/CAVWTPModel.json",set,"MU_3_STD","0.740000"
"config/choice_models/CAVWTPModel.json",set,"MU_4","1.360000"
"config/choice_models/CAVWTPModel.json",set,"MU_4_STD","0.310000"
"config/choice_models/CAVWTPModel.json",set,"STD_CONSTANT","1.500000"
"config/choice_models/CAVWTPModel.json",set,"STD_FACTOR_ADVANCED_MOBILITY","1.310000"
"config/choice_models/CAVWTPModel.json",set,"STD_FACTOR_ENJOY_DRIVE","0.430000"
"config/choice_models/CAVWTPModel.json",set,"STD_FACTOR_ENVIRONMENT","0.410000"
"config/choice_models/CAVWTPModel.json",set,"STD_FACTOR_PUBLIC_TRANSIT","0.430000"
"config/choice_models/CAVWTPModel.json",set,"STD_FUEL_EFFICIENCY_EXP_IMP","1.450000"
"config/choice_models/CAVWTPModel.json",set,"STD_HAS_VEHICLE","3.750000"
"config/choice_models/CAVWTPModel.json",set,"STD_HIGH_PRICE_EXP_IMP","2.330000"
"config/choice_models/CAVWTPModel.json",set,"STD_JOB_ADMIN","0.800000"
"config/choice_models/CAVWTPModel.json",set,"STD_LESS_STRESSFUL_EXP","0.900000"
"config/choice_models/CAVWTPModel.json",set,"STD_LN_VMT","0.060000"
"config/choice_models/CAVWTPModel.json",set,"STD_LONG_DISTANCE_TRIPS","1.400000"
"config/choice_models/CAVWTPModel.json",set,"STD_MAJOR_ACCIDENT","1.900000"
"config/choice_models/CAVWTPModel.json",set,"STD_NO_TELECOMMUTE","0.470000"
"config/choice_models/CAVWTPModel.json",set,"STD_WANTS_AV_TO_WORK","1.250000"
"config/choice_models/CAVWTPModel.json",set,"T_INCOME_HIGH","-0.450000"
"config/choice_models/CAVWTPModel.json",set,"T_INCREASE_SAFETY_EXP_IMP","-0.150000"
"config/choice_models/HouseholdTransactionsModel.json",set,"ASC_HOME","2.012400"
"config/choice_models/HouseholdTransactionsModel.json",set,"ASC_JOB","0.009600"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_1TO2_ROOMS","-0.194500"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_APARTMENT","-0.126400"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_DRIVEMODE","0.120600"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_HHINC_BEGIN","-0.033100"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_MOVE_NOCHANGE","0.010700"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_NUMCHILDREN","-0.091100"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_NUMFEMALE","-0.087800"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_NUMSTUDENT_PRIMARY","0.268800"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_NUMSTUDENT_TERTIARY","0.224300"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_OWN","1.027300"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_RENT","-0.114000"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_HOME_SFH","0.017200"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_ADMINISTRATIVE","0.363400"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_AGE","0.117800"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_FLEXIBLE","0.298200"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_FULLTIME","0.534600"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_GENDER","0.178500"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_INCOME_FINAL","0.861800"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_MANAGER","0.035000"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_NUMJOBS","-0.057800"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_PROFESSIONAL","-0.132600"
"config/choice_models/HouseholdTransactionsModel.json",set,"B_JOB_WFH","-0.361400"
"config/choice_models/HouseholdTransactionsModel.json",set,"SIGMA_HOME","1.364400"
"config/choice_models/HouseholdTransactionsModel.json",set,"SIGMA_JOB","1.320500"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",default,"Ecomm_Deliv_Rate","0.000000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_CONSTANT","-0.007000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_HH_ADULT","-0.047000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_INC_BET_25_50","-0.533000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_INC_BET_50_100","-0.151000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_INC_GT_200","0.398000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_INC_LT_25","-0.456000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"B_RES_TO_XT_STOP_WLK_DIST","0.076000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"R_CONSTANT","2.703000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"R_HH_ADULT","-0.184000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"R_HH_VEHICLE","-0.145000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"R_INC_GT_200","0.389000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"R_WALK_SCORE","-0.047000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"T_THETA1","0.000000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"T_THETA2","1.520000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"T_THETA3","2.095000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"T_THETA4","2.639000"
"config/choice_models/EcommerceChoiceModel_0_08DelRate.json",set,"T_THETA5","3.377000"
"config/choice_models/TransitPassChoiceModel.json",default,"X_ROAD_DEN_HOME","0.000000"
"config/choice_models/TransitPassChoiceModel.json",set,"C_RHO","0.000000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_CONSTANT","-0.848000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_EDU_HIGH","-0.165000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_EMP_FULL_OR_PART","0.278000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_FEMALE","-0.122000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_HOME_OWNER","0.136000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_INC_20_TO_50","-0.104000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_NON_FLEX_WORK_SCHE","0.114000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_ROAD_DEN_WORK","0.067000"
"config/choice_models/TransitPassChoiceModel.json",set,"P_WORK_TTIME_DELTA","-0.427000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_AGE_ABOVE_65","-0.236000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_AGE_LESS_THAN_18","-0.424000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_CONSTANT","-1.401000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_EDU_LOW","-0.152000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_EMP_FULL_OR_PART","-0.063000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_FEMALE","-0.126000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_FULL_TIME_STUDENT","0.367000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_HOME_OWNER","-0.320000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_HOME_TRANS_ACC","0.034000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_INC_LOW","0.196000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_NON_FLEX_WORK_SCHE","-0.151000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_NO_DRIVER_LICENSE","1.013000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_NO_XIT_HOME","-0.301000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_NO_XIT_WORK","-0.435000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_ROAD_DEN_WORK","0.385000"
"config/choice_models/TransitPassChoiceModel.json",set,"X_VEH_AVAIL_FOR_WORK","-0.689000"
"TNCFleetModel.json",default,"use_service_choice","0"
"TNCFleetModel.json",default,"EV_CHARGE_IF_IDLE_FLAG","0"
"TNCFleetModel.json",default,"EV_CHARGE_ON_IDLE_MIN","30.000000"
"TNCFleetModel.json",default,"EV_SERVE_IF_CHARGING_FLAG","0"
"TNCFleetModel.json",default,"SERVICE_CONVENIENCE_FLAG","0"
"TNCFleetModel.json",default,"SERVICE_DISTANCE","2 mi"
"TNCFleetModel.json",default,"SERVICE_FLAG","0"
"TNCFleetModel.json",default,"assignment_strategy","default"
"TNCFleetModel.json",default,"batched_interval_seconds","1 s"
"TNCFleetModel.json",default,"charging_strategy","default"
"TNCFleetModel.json",default,"clean_service_time","10 mins"
"TNCFleetModel.json",default,"fare_strategy","default"
"TNCFleetModel.json",default,"init_vehicle_state_strategy","default"
"TNCFleetModel.json",default,"maintenance_strategy","default"
"TNCFleetModel.json",default,"max_service_trips_per_day","2"
"TNCFleetModel.json",default,"max_tours_before_cleaning","5"
"TNCFleetModel.json",default,"parking_flag","0"
"TNCFleetModel.json",default,"parking_strategy","default"
"TNCFleetModel.json",default,"reposition_strategy","default"
"TNCFleetModel.json",default,"repositioning_flag","0"
"TNCFleetModel.json",default,"service_time","30 mins"
"TNCFleetModel.json",set,"CUTOFF_BATTERY_LEVEL","90.000000"
"TNCFleetModel.json",set,"DRS_FLAG","0"
"TNCFleetModel.json",set,"MIN_EV_SoC","15.000000"
"TNCFleetModel.json",default,"CLOSEST_TREE_ASSIGNMENT","0"
"TNCFleetModel.json",default,"Euclidian_Factor","2.000000"
"TNCFleetModel.json",default,"FORCE_EV_FLEET","0"
"TNCFleetModel.json",default,"STOP_AGG_FLAG","0"
"TNCFleetModel.json",default,"TNC_PREBOOKING_ASSIGNMENT_TIME","2 mins"
"TNCFleetModel.json",default,"TNC_WAIT_ASSIGNMENT_TIME","2 mins"
"TNCFleetModel.json",default,"ZONE_BASED_ASSIGNMENT","1"
"TNCFleetModel.json",default,"base_fare","[3.3 dollars]"
"TNCFleetModel.json",default,"cost_per_mile","[1.25 dpm]"
"TNCFleetModel.json",default,"cost_per_minute","[0.25 dollars_per_minute]"
"TNCFleetModel.json",default,"driver_rating_max","5.000000"
"TNCFleetModel.json",default,"driver_rating_min","3.000000"
"TNCFleetModel.json",default,"human_driver_ratio","0.000000"
"TNCFleetModel.json",default,"human_request_cancellation_prob","0.000000"
"TNCFleetModel.json",default,"market_capture_prob_for_loyalty","1.000000"
"TNCFleetModel.json",default,"overwrite_input_party_size","0"
"TNCFleetModel.json",default,"party_size_distribution","[]"
"TNCFleetModel.json",default,"pooling_discount_for_fare","0.400000"
"TNCFleetModel.json",default,"services_offered","[]"
"TNCFleetModel.json",default,"tncandride_base_fare","[0 dollars]"
"TNCFleetModel.json",default,"tncandride_cost_per_mile","[2.25 dpm]"
"TNCFleetModel.json",default,"tncandride_cost_per_minute","[0.33 dollars_per_minute]"
"TNCFleetModel.json",default,"uniform_service_dist","1"
"TNCFleetModel.json",set,"EV_INITIAL_SoC_RANGE_FLAG","1"
"TNCFleetModel.json",set,"EV_INIT_SoC_MEAN","70.000000"
"TNCFleetModel.json",set,"EV_INIT_SoC_SD","5.000000"
"TNCFleetModel.json",set,"EV_RANGE_MILES","100 mi"
"TNCFleetModel.json",set,"SAME_EV_RANGE_FLAG","1"
"TNCFleetModel.json",set,"TNC_FLEET_SIZE","1200"
"TNCFleetModel.json",set,"TNC_MAX_SEATED_CAPACITY","4.000000"
"TNCFleetModel.json",set,"TNC_MAX_WAIT_TIME","10 mins"
"TNCFleetModel.json",set,"delivery_type","person"
"TNCFleetModel.json",set,"force_tnc_over_auto","0"
"TNCFleetModel.json",set,"geofence_flag","0"
"TNCFleetModel.json",set,"strategy_name","default_strat"
"TNCFleetModel.json",set,"use_fmlm","0"
"TNCFleetModel.json",set,"NO_OF_OPERATORS","1"
"TNCFleetModel.json",set,"OP_1","Operator_1"
"TNCFleetModel.json",set,"TNC_LOGGING_INTERVAL","15 mins"

,duration,sql
0,0.00012683868408203125,DROP TABLE IF EXISTS Activity_Start_Distribution
1,5.173683166503906e-05,DROP TABLE IF EXISTS Activity_Start_Distribution_by_TOD
2,0.3433816432952881,"CREATE TABLE Activity_Start_Distribution As
SELECT
    (CASE WHEN a.trip == 0 THEN 'planned' ELSE 'executed' END) as activity_stage,
    cast(start_time/3600 as int) as start_time,
    4.0 * sum(CASE WHEN type='EAT OUT'         THEN 1 END) as EAT_OUT,
    4.0 * sum(CASE WHEN type='ERRANDS'         THEN 1 END) as ERRANDS,
    4.0 * sum(CASE WHEN type='HEALTHCARE'      THEN 1 END) as HEALTHCARE,
    4.0 * sum(CASE WHEN type='LEISURE'         THEN 1 END) as LEISURE,
    4.0 * sum(CASE WHEN type='PERSONAL'        THEN 1 END) as PERSONAL,
    4.0 * sum(CASE WHEN type='RELIGIOUS-CIVIC' THEN 1 END) as RELIGIOUS,
    4.0 * sum(CASE WHEN type='SERVICE'         THEN 1 END) as SERVICE,
    4.0 * sum(CASE WHEN type='SHOP-MAJOR'      THEN 1 END) as SHOP_MAJOR,
    4.0 * sum(CASE WHEN type='SHOP-OTHER'      THEN 1 END) as SHOP_OTHER,
    4.0 * sum(CASE WHEN type='SOCIAL'          THEN 1 END) as SOCIAL,
    4.0 * sum(CASE WHEN type='WORK'            THEN 1 END) as WORK,
    4.0 * sum(CASE WHEN type='PART_WORK'       THEN 1 END) as WORK_PART,
    4.0 * sum(CASE WHEN type='WORK AT HOME'    THEN 1 END) as WORK_HOME,
    4.0 * sum(CASE WHEN type='SCHOOL'          THEN 1 END) as SCHOOL,
    4.0 * sum(CASE WHEN type='PICKUP-DROPOFF'  THEN 1 END) as PICKUP,
    4.0 * sum(CASE WHEN type='HOME'            THEN 1 END) as HOME,
    4.0 * sum(1) AS total
FROM Activity a
WHERE (a.trip == 0 OR (a.Start_Time > 122 and a.trip <> 0))
  AND NOT (mode = 'NO_MOVE' and type in ('HOME','WORK AT HOME','PICKUP-DROPOFF'))
GROUP BY 1,2"
3,0.008845090866088867,"CREATE TABLE Activity_Start_Distribution_by_TOD as
SELECT
    CASE
           WHEN ""start_time"" <= 5                         THEN '1.NIGHT'
           WHEN ""start_time"" >= 6  AND ""start_time"" <= 8  THEN '2.AMPEAK'
           WHEN ""start_time"" >= 9  AND ""start_time"" <= 11 THEN '3.AMOFFPEAK'
           WHEN ""start_time"" >= 12 AND ""start_time"" <= 15 THEN '4.PMOFFPEAK'
           WHEN ""start_time"" >= 16 AND ""start_time"" <= 18 THEN '5.PMPEAK'
           WHEN ""start_time"" >= 19 AND ""start_time"" <= 23 THEN '6.EVENING'
           END as time_of_day,
    activity_stage,
    sum(""EAT_OUT"") as EAT_OUT,
    sum(""ERRANDS"") as ERRANDS,
    sum(""HEALTHCARE"") as HEALTHCARE,
    sum(""LEISURE"") as LEISURE,
    sum(""PERSONAL"") as PERSONAL,
    sum(""RELIGIOUS"") as RELIGIOUS,
    sum(""SERVICE"") as SERVICE,
    sum(""SHOP_MAJOR"") as SHOP_MAJOR,
    sum(""SHOP_OTHER"") as SHOP_OTHER,
    sum(""SOCIAL"") as SOCIAL,
    sum(""WORK"") as ""WORK"",
    sum(""WORK_PART"") as WORK_PART,
    sum(""WORK_HOME"") as WORK_HOME,
    sum(""SCHOOL"") as SCHOOL,
    sum(""PICKUP"") as PICKUP,
    sum(""HOME"") as HOME,
    sum(""total"") as Total
FROM
    ""Activity_Start_Distribution""
group by
    1,2"

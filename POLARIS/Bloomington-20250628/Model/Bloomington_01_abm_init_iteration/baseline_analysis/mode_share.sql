DROP TABLE IF EXISTS Mode_Distribution_ADULT;

CREATE TABLE IF NOT EXISTS mode_Distribution_ADULT AS
SELECT
    mode,
    4.0 * sum(trip.destination = person.work_location_id) AS 'HBW',
    4.0 * sum(trip.origin == household.location AND trip.destination <> person.work_location_id) AS 'HBO',
    4.0 * sum(trip.origin <> household.location AND trip.destination <> household.location AND trip.destination <> person.work_location_id) AS 'NHB',
    4.0 * count(*) AS total
FROM
    trip,
    person,
    household
WHERE
    trip.person = person.person
    AND person.household = household.household
    AND person.age > 16
    AND trip."end" - trip."start" > 2
GROUP BY
    mode;
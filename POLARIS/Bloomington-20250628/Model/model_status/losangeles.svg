<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="172" height="20">
    <linearGradient id="b" x2="0" y2="100%">
        <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
        <stop offset="1" stop-opacity=".1"/>
    </linearGradient>
    <mask id="anybadge_1">
        <rect width="172" height="20" rx="3" fill="#fff"/>
    </mask>
    <g mask="url(#anybadge_1)">
        <path fill="#555" d="M0 0h122v20H0z"/>
        <path fill="#E05D44" d="M122 0h50v20H122z"/>
        <path fill="url(#b)" d="M0 0h172v20H0z"/>
    </g>
    <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
        <text x="62.0" y="15" fill="#010101" fill-opacity=".3">2025-06-26 02:38Z</text>
        <text x="61.0" y="14">2025-06-26 02:38Z</text>
    </g>
    <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
        <text x="148.0" y="15" fill="#010101" fill-opacity=".3">FAILED</text>
        <text x="147.0" y="14">FAILED</text>
    </g>
</svg>

{"General simulation controls": {"starting_time_hh_mm": "00:00", "ending_time_hh_mm": "24:00", "simulation_interval_length_in_second": 1, "num_simulation_intervals_per_assignment_interval": 60, "seed": 1234567, "database_name": "Bloomington"}, "Network simulation controls": {"traffic_model": "LAGRANGIAN", "lagrangian_coordinates_sub_steps": 2, "rng_type": "DETERMINISTIC", "node_control_flag": 1, "jam_density_constraints_enforced": true, "maximum_flow_rate_constraints_enforced": true, "merging_mode": "PRIORITY_BASED", "use_realtime_travel_time_for_enroute_switching": false, "pretrip_informed_market_share": 0.75, "realtime_informed_vehicle_market_share": 0.0, "information_compliance_rate_mean": 0.25, "information_compliance_rate_standard_deviation": 0.0, "relative_indifference_bound_route_choice_mean": 0.1, "minimum_travel_time_saving_mean": 1.0, "minimum_travel_time_saving_standard_deviation": 1.0, "minimum_delay_ratio_for_enroute_switching": 3.0, "minimum_delay_seconds_for_enroute_switching": 600.0, "minimum_delay_ratio_for_look_ahead_enroute_switching": 1.5, "minimum_delay_seconds_for_look_ahead_enroute_switching": 300.0, "minimum_time_between_look_ahead_switches_seconds": 600.0, "minimum_seconds_from_arrival_for_enroute_switching": 150.0, "minimum_link_delay_ratio_for_enroute_switching": 4.0, "minimum_link_delay_seconds_for_enroute_switching": 30.0, "jam_density": 200.0, "simulate_parking": false, "convergence_mode": false, "capacity_expressway": 2011.0, "capacity_arterial": 1740.0, "capacity_local": 1213.0, "piecewise_linear_fd": true, "beta_piecewise_linear_fd": 2.0, "gamma_piecewise_linear_fd": 0.25, "maximum_link_delay_ratio": 160.0, "threshold_conflicting_signalized_seconds": 2.0, "threshold_conflicting_all_stop_seconds": 4.0, "threshold_conflicting_two_way_stop_seconds": 6.0, "enroute_switching_cv": false, "fix_connectivity_penetration_rate_cv": false, "connectivity_penetration_rate_cv": 0.0, "enroute_switching_on_excessive_delay": true, "enroute_switching_use_cv_info": false, "rsu_enabled_switching": false, "use_traffic_api": false, "multiclass_definition": "LINK_TYPE_BY_REGULAR_AV_TRUCK", "traffic_multiclass_parameters": {"av_reaction_multiplier_expressway": 0.6, "av_reaction_multiplier_local": 1.0, "av_reaction_multiplier_signalized": 1.0, "freight_speed_multiplier_signalized": 1.0, "freight_speed_multiplier_local": 1.0, "freight_speed_multiplier_expressway": 1.0, "cacc_freight_reaction_time_multiplier_expressway": 1.0, "cacc_freight_reaction_time_multiplier_signalized": 1.0, "cacc_freight_reaction_time_multiplier_local": 1.0}, "spacing_shift_by_mode": {"MD_TRUCK": 0.0, "HD_TRUCK": 0.0, "BPLATE": 0.0, "LD_TRUCK": 0.0}}, "Routing and skimming controls": {"enroute_switching_enabled": true, "aggregate_routing": true, "time_dependent_routing": false, "time_dependent_routing_weight_shape": 2.5, "time_dependent_routing_weight_scale": 900.0, "time_dependent_routing_weight_factor": 1.0, "input_result_database_name": "Bloomington", "multimodal_routing": true, "multimodal_routing_model_file": "MultiModalRoutingModel.json", "input_highway_skim_file": "highway_skim_file.omx", "input_transit_skim_file": "transit_skim_file.omx", "skim_averaging_factor": 0.75, "read_skim_tables": false, "generate_transit_skims": true, "skim_nodes_per_zone": 4, "read_trajectories": false}, "Population synthesizer controls": {"popsyn_control_file": "linker_file.txt", "read_population_from_database": false, "percent_to_synthesize": 0.0, "traffic_scale_factor": 0.25, "ipf_tolerance": 0.01, "marginal_tolerance": 5, "maximum_iterations": 100, "write_marginal_output": false, "write_full_output": false, "replan": {}}, "ABM Controls": {"activity_generation_model_file": "config/choice_models/ActivityGenerationModel.json", "mode_choice_model_file": "config/choice_models/ModeChoiceModel.json", "destination_choice_model_file": "config/choice_models/DestinationChoiceModel.json", "telecommute_choice_model_file": "config/choice_models/TelecommuteChoiceModel.json", "transit_pass_choice_model_file": "config/choice_models/TransitPassChoiceModel.json", "timing_choice_model_file": "config/choice_models/TimingChoiceModel.json", "cav_wtp_model_file": "config/choice_models/CAVWTPModel.json", "ecommerce_choice_model_file": "config/choice_models/EcommerceChoiceModel_0_08DelRate.json", "escooter_use_level_choice_model_file": "config/choice_models/EscooterUseLevelChoiceModel.json", "household_transactions_model_file": "config/choice_models/HouseholdTransactionsModel.json", "fleet_vehicle_distribution_file_name": "fleet_vehicle_distribution.txt", "vehicle_distribution_file_name": "vehicle_distribution.txt", "use_tnc_system": true, "tnc_fleet_model_file": "TNCFleetModel.json", "read_trip_factors": {"External": 0.0}}, "Scenario controls": {"flexible_work_percentage": 0.13, "rideshare_cost_per_mile": 1.25, "rideshare_cost_per_minute": 0.25, "rideshare_base_fare": 3.3}, "Output controls": {"output_directory": "Bloomington_00_skim_iteration", "output_moe_for_assignment_interval": false, "output_link_moe_for_simulation_interval": false, "output_link_moe_for_assignment_interval": true, "output_turn_movement_moe_for_assignment_interval": true, "output_turn_movement_moe_for_simulation_interval": false, "output_network_moe_for_simulation_interval": false, "write_skim_tables": true, "write_activity_output": true, "write_demand_to_database": true, "write_vehicle_trajectory": true, "vehicle_trajectory_sample_rate": 0.0, "output_highway_skim_file": "highway_skim_file.omx", "output_transit_skim_file": "transit_skim_file.omx"}, "CRISTAL Controls": {"model_freight": false, "CRISTAL_filename_par_Assets": "CRISTAL_Model_Parameters_Assets.json", "CRISTAL_filename_par_MC_ShSize": "CRISTAL_Model_Parameters_MC_ShSize.json", "CRISTAL_filename_Heuristic_Setting": "CRISTAL_Heuristic_Parameters.json", "max_n_suppliers_to_model": 2, "percent_firms_to_model": 0.01, "percent_firms_to_output": 0.01, "percent_supply_chains_to_output": 0.01, "percent_carriers_to_output": 0.01, "percent_shipments_to_output_truck": 0.01, "percent_shipments_to_output_rail": 0.01, "percent_shipments_to_output_other": 0.01, "percent_B2B_delivery_rosters_to_output": 0.01, "use_b2c_routing": false, "use_freight_model": false}, "skim_interval_endpoint_minutes": [240, 360, 420, 480, 540, 600, 720, 840, 900, 960, 1020, 1080, 1140, 1200, 1320, 1440], "EV_charging": false, "tnc_feedback": false}
{"Path_Choice_Parameters_JSON": {"text_variable": "DDD", "a03": 1, "a04": 2, "a05": 3, "a06": 4, "__comment": "Inputs for Frank-Copula model", "BulkHandFee": 10, "WDCHandFee": 15, "IntermodalexchangeHandFee": 15, "TransfloadHandFee": 10, "AirHandFee": 50, "WaterRate": 0.0274, "RailRate": 0.05, "IntermodalexchangeRate": 0.06, "TruckRate_200mi": 3.09, "TruckRate_200_500mi": 2.39, "TruckRate_500_1000mi": 1.93, "TruckRate_1000_1500mi": 1.75, "TruckRate_1500plus": 1.51, "Truck_averagerate": 2.25, "AirRate_1000mi": 3.8, "AirRate_1000_2000mi": 1.8, "AirRate_2000plus": 1.0, "RailRate_200mi": 0.5, "RailRate_200_500mi": 0.2, "RailRate_500_1500mi": 0.125, "RailRate_1500_2000mi": 0.075, "RailRate_2000plus": 0.05, "LTLRate": 0.18, "FTL53Rate": 0.15, "RailMileRatio": 1.3, "Accessmileratio": 0.1, "Egressmileratio": 0.1, "TrkCostPerMile": 1.65, "WaterMPH": 5, "RailMPH": 30, "LHTruckMPH": 55, "DrayTruckMPH": 55, "AirMPH": 550, "CapacityFTL53": 20, "CapacityRail": 80, "CapacityAirplane": 25, "CapacityLTL": 3.5, "MaxDailyDrivHrs": 11, "ExpressSurcharge": 1.5, "BulkTime": 12, "WDCTime": 24, "IntermodalexchangeTime": 24, "TloadTime": 12, "AirTime": 1, "CarloadDelay": 0.064865, "dppBulk": 2000, "dppInt": 10000, "dppFin": 50000, "dppOth": 10000, "dppAnimals": 4000, "DiscountBulk": 0.05, "DiscountInt": 0.15, "DiscountFin": 0.4, "DiscountAnimals": 0.15, "DiscountOth": 0.15, "StorageBulk": 5, "StorageInt": 50, "StorageFin": 150, "StorageAnimals": 10, "StorageOth": 50, "AirDamagedRatio": 0.03, "TruckDamagedRatio": 0.05, "RailDamagedRatio": 0.1, "_Beta": [[0.0, 0.53, 0.0, 0.0, 0.0, -0.873, 0.0, 0.0, -0.452, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [-14.96, 0.0, 0.0, 0.0, 0.0, 0.0, 0.953, 0.584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.865, 0.0, 0.0, 0.0], [-4.534, 0.0, 1.354, 0.631, 0.0, 0.0, 0.0, 0.0, 0.0, 0.956, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.078, 0.0, 0.0, 0.0, 0.0, 0.0], [-3.825, 0.0, -2.256, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.766, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]], "_Modecost": [-0.391, -0.378, -0.723, -0.752], "_Gamma": [[0.725, 0, 0, 0.467, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.006, 0, 0.0004, 0, 0, 0, -1.06], [-0.205, 0, 0, 0, 0.338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0003, 0, 1.353, 1.02, 0], [0, 0.399, 0, 0, 0, 1.477, 1.084, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.004, 0, 0, 0, 1.24, 0.932, 0], [1.515, 1.005, 0, 0, 0, 1.079, 0.74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0002, 0, 0, 0, -0.808], [1.934, 1.709, 0.893, 0, 0.666, 2.33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.255, 0, 0, 0, 0, 0.293, 0]], "_Theta": [6.165, 4.63, 6.566, 7.469]}}
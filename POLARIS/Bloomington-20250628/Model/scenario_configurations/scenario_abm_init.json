{"General simulation controls": {"starting_time_hh_mm": "00:00", "ending_time_hh_mm": "24:00", "simulation_interval_length_in_second": 1, "num_simulation_intervals_per_assignment_interval": 60, "seed": 1234567, "database_name": "bloomington"}, "Network simulation controls": {"rng_type": "DETERMINISTIC", "node_control_flag": 1, "jam_density_constraints_enforced": true, "maximum_flow_rate_constraints_enforced": true, "merging_mode": "PRIORITY_BASED", "use_realtime_travel_time_for_enroute_switching": false, "pretrip_informed_market_share": 0.75, "realtime_informed_vehicle_market_share": 0.0, "information_compliance_rate_mean": 0.25, "information_compliance_rate_standard_deviation": 0.0, "relative_indifference_bound_route_choice_mean": 0.1, "minimum_travel_time_saving_mean": 1.0, "minimum_travel_time_saving_standard_deviation": 1.0, "minimum_delay_ratio_for_enroute_switching": 1.5, "minimum_delay_seconds_for_enroute_switching": 100, "minimum_seconds_from_arrival_for_enroute_switching": 150, "maximum_link_delay_ratio": 80.0}, "Routing and skimming controls": {"enroute_switching_enabled": true, "aggregate_routing": true, "time_dependent_routing": false, "time_dependent_routing_weight_shape": 2.5, "time_dependent_routing_weight_scale": 900.0, "time_dependent_routing_weight_factor": 1.0, "historical_results_database_name": "bloomington", "multimodal_routing": true, "multimodal_routing_model_file": "MultiModalRoutingModel.json", "input_highway_skim_file_path_name": "highway_skim_file.bin", "input_transit_skim_file_path_name": "transit_skim_file.bin", "skim_averaging_factor": 0.75, "read_skim_tables": true, "generate_transit_skims": false, "skim_nodes_per_zone": 6, "read_trajectories": false}, "Population synthesizer controls": {"popsyn_control_file": "linker_file.txt", "read_population_from_database": false, "percent_to_synthesize": 1.0, "demand_reduction_factor": 0.0, "traffic_scale_factor": 1.0, "ipf_tolerance": 0.01, "marginal_tolerance": 5, "maximum_iterations": 100, "write_marginal_output": false, "write_full_output": false}, "ABM Controls": {"read_demand_from_database": true, "activity_generation_model_file": "BloomingtonActivityGenerationModel.json", "mode_choice_model_file": "BloomingtonModeChoiceModel.json", "destination_choice_model_file": "BloomingtonDestinationChoiceModel.json", "telecommute_choice_model_file": "BloomingtonTelecommuteChoiceModel.json", "timing_choice_model_file": "BloomingtonTimingChoiceModel.json", "fleet_vehicle_distribution_file_name": "fleet_vehicle_distribution.txt", "vehicle_distribution_file_name": "vehicle_distribution.txt", "use_tnc_system": true, "tnc_fleet_model_file": "TNCFleetModel.json"}, "Scenario controls": {"flexible_work_percentage": 0.13, "rideshare_cost_per_mile": 1.25, "rideshare_cost_per_minute": 0.25, "rideshare_base_fare": 3.3}, "Output controls": {"output_dir_name": "bloomington", "output_moe_for_assignment_interval": false, "output_link_moe_for_simulation_interval": false, "output_link_moe_for_assignment_interval": true, "output_turn_movement_moe_for_assignment_interval": true, "output_turn_movement_moe_for_simulation_interval": false, "output_network_moe_for_simulation_interval": false, "write_skim_tables": true, "write_activity_output": true, "write_demand_to_database": true, "write_vehicle_trajectory": true, "vehicle_trajectory_sample_rate": 0.04, "output_highway_skim_file_path_name": "highway_skim_file.bin", "output_transit_skim_file_path_name": "transit_skim_file.bin"}}